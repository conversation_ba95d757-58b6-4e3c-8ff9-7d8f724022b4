<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\UserFcmToken;
use App\Support\Facades\Core;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;

class UserFcmTokenController extends Controller
{
    public function store(Request $request)
    {
        $user = $request->user();
        $partner = Core::currentPartner();

        $validate = Validator::make([
            'token' => $request->input('token'),
        ], [
            'token' => [
                'required',
                Rule::unique('user_fcm_tokens')->where(function ($query) use ($partner, $user) {
                    return $query->where('user_id', $user->getKey())
                        ->where('partner_id', $partner->getKey());
                }),
            ],
        ]);

        if ($validate->fails()) {
            return response()->noContent();
        }

        UserFcmToken::query()->updateOrCreate([
            'token' => $token = $request->input('token'),
            'user_id' => $user->getKey(),
            'partner_id' => $partner->getKey(),
        ], [
            'token' => $token,
        ]);

        return response()->noContent();
    }

    public function show(string $token, Request $request)
    {
        $user = $request->user();
        $partner = Core::currentPartner();

        $fcmToken = UserFcmToken::query()
            ->where('user_id', $user->getKey())
            ->where('partner_id', $partner->getKey())
            ->where('token', $token)
            ->exists();

        return response()->json([
            'data' => $fcmToken,
        ]);
    }

    public function destroy(string $token, Request $request)
    {
        $user = $request->user();
        $partner = Core::currentPartner();

        $fcmToken = UserFcmToken::query()
            ->where('user_id', $user->getKey())
            ->where('partner_id', $partner->getKey())
            ->where('token', $token)
            ->first();

        if ($fcmToken) {
            $fcmToken->delete();
        }

        return response()->noContent();
    }
}
