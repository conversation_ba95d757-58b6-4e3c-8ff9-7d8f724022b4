<?php

namespace App\Http\JsonResources\Recipes;

use Illuminate\Http\Resources\Json\JsonResource;

class RecipeIngredientJsonResource extends JsonResource
{
    public function toArray($request): array
    {
        $amount = $this->resource->amount;

        $amount = $amount ? (is_array($amount) ? $amount : json_decode($amount)) : [];

        return [
            'name' => $this->resource->name,
            'image' => $this->resource->image,
            'amount' => $amount,
        ];
    }
}
