import { AppBar, AppLayout, Button, LoadingPage, Navigation } from '@/components/index.js'
import useCheckout from '@/hooks/checkout.js'
import { useParams } from 'react-router-dom'
import NotFoundPage from '@/routes/NotFoundPage.jsx'

export default function PaymentOptionPage() {
  const { id } = useParams()
  const { product, loading, checkout } = useCheckout({ packageId: id })

  const handleCheckout = async () => {
    await checkout()
  }

  if (loading) {
    return <LoadingPage />
  }

  if (!product && !loading) {
    return <NotFoundPage />
  }

  return (
    <AppLayout>
      <AppBar leading={<AppBar.BackAction />}>
        <AppBar.Title> Payment Options </AppBar.Title>
      </AppBar>

      <div className="mb-28 mt-16 py-4">
        <div className="flex min-h-24 items-center justify-center bg-slate-200 py-4">
          <div className="w-1/2 border border-white p-3 text-center">
            <div className="text-2xl font-bold text-secondary">{product.name}</div>
            <div className="text-sm font-medium">Membership Plan</div>
            <div className="mt-2 text-center">
              <span className="text-2xl font-bold text-primary">${product.price}</span>
              <span className="text-sm">/mo.</span>
            </div>
          </div>
        </div>

        <div className="p-4">
          {/*<div className="mb-4 text-xl font-bold"> Your Card</div>*/}

          <Button
            onClick={() => handleCheckout()}
            className="mb-3 flex w-full items-center justify-center gap-1 !rounded-none font-normal"
            variant="secondary"
          >
            Checkout
          </Button>

          {/*<div className="mb-4 rounded-2xl bg-blue-200 p-6">*/}
          {/*  <div className="mb-6 font-medium"> Alena Syabian</div>*/}
          {/*  <div className="mb-2 h-8 w-12 rounded-md bg-white"></div>*/}

          {/*  <div className="mb-2 text-xl font-bold">*/}
          {/*    <span>4241</span>*/}
          {/*    <span className="ms-3">9214</span>*/}
          {/*    <span className="ms-3">7219</span>*/}
          {/*    <span className="ms-3">3456</span>*/}
          {/*  </div>*/}

          {/*  <div className="font-normal">12/24</div>*/}
          {/*</div>*/}

          {/*<div className="mb-4 text-xl font-bold"> Other Payment Methods </div>*/}

          {/*<Button*/}
          {/*  onClick={() => handleCheckout()}*/}
          {/*  className="mb-3 flex w-full items-center justify-center gap-1 !rounded-none font-normal"*/}
          {/*  variant="primary"*/}
          {/*>*/}
          {/*  <GoogleIcon className="h-5 w-5" /> Pay*/}
          {/*</Button>*/}

          {/*<Button className="flex w-full items-center justify-center gap-1 !rounded-none bg-yellow-400 font-normal">*/}
          {/*  <AmazonIcon className="h-6 w-12 text-primary" /> Pay*/}
          {/*</Button>*/}

          {/*<Button*/}
          {/*  variant="alternative"*/}
          {/*  className="mt-3 flex w-full items-center justify-center !border-secondary text-secondary"*/}
          {/*  size="lg"*/}
          {/*>*/}
          {/*  <CirclePlusIcon className="h-6 w-12 text-secondary" />*/}
          {/*  Add New Card*/}
          {/*</Button>*/}
        </div>
      </div>

      <Navigation />
    </AppLayout>
  )
}
