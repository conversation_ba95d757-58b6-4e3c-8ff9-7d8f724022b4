<?php

namespace App\Filament\Admin\Resources;

use App\Enums\IngredientLevel;
use App\Filament\Admin\Resources\IngredientResource\Pages;
use App\Filament\Admin\Resources\Traits\HasPermission;
use App\Filament\Imports\IngredientImporter;
use App\Filament\Tables\Actions\ImportAction;
use App\Models\Ingredient;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\FontFamily;
use Filament\Tables;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class IngredientResource extends Resource
{
    use HasPermission;

    protected static ?string $model = Ingredient::class;

    public static function getNavigationGroup(): ?string
    {
        return __('Content');
    }

    public static function getNavigationIcon(): string
    {
        return 'heroicon-o-tag';
    }

    public static function getNavigationSort(): int
    {
        return 0;
    }

    public static function form(Form $form): Form
    {
        return $form->schema([
            Section::make()
                ->schema([
                    Grid::make()
                        ->schema([
                            TextInput::make('spoonacular_id')
                                ->label('Spoonacular ID')
                                ->helperText(__('The Spoonacular ID of the ingredient.')),
                            TextInput::make('name')
                                ->label(__('Name'))
                                ->required(),
                        ]),
                    Grid::make()
                        ->columns(3)
                        ->schema([
                            Select::make('oxalate')
                                ->label(__('Oxalate Level'))
                                ->options(IngredientLevel::class)
                                ->required(),
                            Select::make('histamine')
                                ->label(__('Histamine Level'))
                                ->options(IngredientLevel::class)
                                ->required(),
                            Select::make('sulfur')
                                ->label(__('Sulfur Level'))
                                ->options(IngredientLevel::class)
                                ->required(),
                            Select::make('salicylate')
                                ->label(__('Salicylate Level'))
                                ->options(IngredientLevel::class)
                                ->required(),
                            Select::make('fodmap')
                                ->label(__('FODMAP Level'))
                                ->options(IngredientLevel::class)
                                ->required(),
                        ]),
                    Toggle::make('is_excluded')
                        ->label(__('Exclude Ingredient?'))
                        ->required(),
                ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('spoonacular_id')
                    ->searchable()
                    ->sortable()
                    ->label('Spoonacular ID')
                    ->default('-')
                    ->fontFamily(FontFamily::Mono),
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->label(__('Ingredient')),
                TextColumn::make('oxalate')
                    ->badge()
                    ->color(fn(IngredientLevel $state) => $state->getColor())
                    ->formatStateUsing(fn(IngredientLevel $state) => $state->getLabel())
                    ->label(__('Oxalate')),
                TextColumn::make('histamine')
                    ->badge()
                    ->color(fn(IngredientLevel $state) => $state->getColor())
                    ->formatStateUsing(fn(IngredientLevel $state) => $state->getLabel())
                    ->label(__('Histamine')),
                TextColumn::make('sulfur')
                    ->badge()
                    ->color(fn(IngredientLevel $state) => $state->getColor())
                    ->formatStateUsing(fn(IngredientLevel $state) => $state->getLabel())
                    ->label(__('Sulfur')),
                TextColumn::make('salicylate')
                    ->badge()
                    ->color(fn(IngredientLevel $state) => $state->getColor())
                    ->formatStateUsing(fn(IngredientLevel $state) => $state->getLabel())
                    ->label(__('Salicylate')),
                TextColumn::make('fodmap')
                    ->badge()
                    ->color(fn(IngredientLevel $state) => $state->getColor())
                    ->formatStateUsing(fn(IngredientLevel $state) => $state->getLabel())
                    ->label(__('FODMAP')),
                Tables\Columns\ToggleColumn::make('is_excluded')
                    ->label(__('Excluded?')),
            ])
            ->headerActions([
                ImportAction::make('importIngredients')
                    ->label(__('Import Ingredients'))
                    ->importer(IngredientImporter::class),
            ])
            ->actions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListIngredients::route('/'),
            'create' => Pages\CreateIngredients::route('/create'),
            'edit' => Pages\EditIngredients::route('/{record}/edit'),
        ];
    }
}
