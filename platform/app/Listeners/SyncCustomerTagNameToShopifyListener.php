<?php

namespace App\Listeners;

use App\Events\PartnerShopifyChangeCustomerTagEvent;
use App\Jobs\SyncCustomerTagsToShopify;
use App\Models\PartnerMeta;
use App\Models\ShopifyUserSyncLog;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;

class SyncCustomerTagNameToShopifyListener implements ShouldQueue
{
    public function handle(PartnerShopifyChangeCustomerTagEvent $event): void
    {
        $partner = $event->partner;

        ShopifyUserSyncLog::query()
            ->where('partner_id', $partner->id)
            ->delete();

        $users = User::query()
            ->select('id')
            ->whereHas('partners', function ($query) use ($partner) {
                $query->where('partners.id', $partner->id);
            })
            ->whereHas('subscriptions', function ($query) {
                $query->where('stripe_status', 'active');
            })
            ->pluck('id')
            ->all();

        $total = count($users);

        PartnerMeta::query()->upsert([
            [
                'partner_id' => $partner->id,
                'name' => 'sync_users_to_shopify_total',
                'value' => $total,
            ],
            [
                'partner_id' => $partner->id,
                'name' => 'sync_users_to_shopify_is_processing',
                'value' => !($total == 0),
            ],
            [
                'partner_id' => $partner->id,
                'name' => 'synced_users_to_shopify',
                'value' => $total == 0,
            ],
        ], ['partner_id', 'name'], ['value']);

        $index = 0;

        $chunks = array_chunk($users, 5);

        foreach ($chunks as $chunk) {
            SyncCustomerTagsToShopify::dispatch($partner, $chunk, $total, $event->currentTag, $event->newTag)->delay(now()->addSeconds($index));
            $index++;
        }
    }
}
