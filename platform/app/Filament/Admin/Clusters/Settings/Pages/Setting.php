<?php

namespace App\Filament\Admin\Clusters\Settings\Pages;

use App\Filament\Admin\Clusters\Settings;
use App\Filament\Admin\Clusters\Traits\HasPermission;
use App\Support\Facades\Setting as SettingFacade;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;

abstract class Setting extends Page implements HasActions, HasForms
{
    use InteractsWithActions;
    use InteractsWithForms;
    use HasPermission;

    protected static ?string $cluster = Settings::class;

    protected static string $view = 'filament.admin.clusters.settings.pages.index';

    protected array $settingKeys = [];

    public array $data = [];

    public static function getNavigationIcon(): string
    {
        return 'heroicon-o-cog';
    }

    public function mount(): void
    {
        $data = [];

        foreach ($this->settingKeys as $settingKey) {
            $data[$settingKey] = SettingFacade::get($settingKey);
        }

        $this->form->fill($data);
    }

    public function save(): void
    {
        $this->callHook('beforeValidate');

        $data = $this->form->getState();

        SettingFacade::set($data);
        SettingFacade::save();

        $this->afterSave($data);

        Notification::make()
            ->success()
            ->title(__('Settings saved.'))
            ->send();
    }

    public function afterSave(array $data): void
    {
        return;
    }

    public function form(Form $form): Form
    {
        return parent::form($form)->statePath('data');
    }

    protected function getFormActions(): array
    {
        return [
            Action::make('save')
                ->label(__('filament-panels::resources/pages/edit-record.form.actions.save.label'))
                ->submit('save')
                ->keyBindings(['mod+s']),
        ];
    }
}
