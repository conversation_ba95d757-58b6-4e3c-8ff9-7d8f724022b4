import { useNavigate, useParams } from 'react-router-dom'
import {
  AppBar,
  AppLayout,
  Button,
  HeartIcon,
  LoadingPage,
  Navigation,
  QuestionGroup,
  QuestionItem,
  Spinner,
  SpinnerPage
} from '@/components'
import { useHealthQuestionnaire } from '@/hooks/healthQuestionnaire'
import NotFoundPage from '@/routes/NotFoundPage'
import { useEffect } from 'react'

export default function HealQuestionnaireDetailPage() {
  const { id } = useParams()
  const navigate = useNavigate()

  const {
    data: healthQuestionnaireData,
    groupedInputs,
    loading,
    processing,
    isMultipleGroup,
    setAnswer,
    getAnswerValue,
    setOffPageAnswer,
    submit
  } = useHealthQuestionnaire(id)

  const saveAnswer = async (e) => {
    e.preventDefault()

    await submit()
  }

  useEffect(() => {
    if (id === 'rx') {
      navigate('/health-questionnaire/medications')
    }
  }, [id, navigate])

  if (loading) {
    return (
      <div className="text-center">
        <LoadingPage />
      </div>
    )
  }

  if (!healthQuestionnaireData && id !== 'rx') {
    return <NotFoundPage />
  }

  return (
    <AppLayout>
      <AppBar leading={<AppBar.BackAction />}>
        <AppBar.Title> Health Questionnaire </AppBar.Title>
      </AppBar>

      <form onSubmit={saveAnswer} className="relative mb-28 mt-16 p-4">
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center gap-2 text-lg font-bold">
            <HeartIcon className="h-6 w-6" />
            {healthQuestionnaireData['pageTitle']}
          </div>
          {healthQuestionnaireData['pageTitle'] !== 'Smart Questions' && (
            <div className="text-lg font-bold"> {healthQuestionnaireData['completions']['completion'] || 0}%</div>
          )}
        </div>

        {healthQuestionnaireData['pageDescription'] && (
          <div className="mb-4 text-primary text-opacity-80">{healthQuestionnaireData['pageDescription']}</div>
        )}

        {isMultipleGroup ? (
          <QuestionGroup
            setOffPageAnswer={setOffPageAnswer}
            pages={healthQuestionnaireData.pages}
            inputs={groupedInputs}
            chooseAnswer={(inputId, value, groupId) => setAnswer(inputId, value, groupId)}
            getAnswerValue={getAnswerValue}
          />
        ) : (
          groupedInputs.map((input, index) => {
            return (
              <QuestionItem
                getAnswerValue={getAnswerValue}
                chooseAnswer={(inputId, value, groupId) => setAnswer(inputId, value, groupId)}
                key={`question-input-${input['@attributes']['input_id']}`}
                inputs={groupedInputs}
                index={index}
              />
            )
          })
        )}

        <Button disabled={processing} className="mt-6 flex w-full justify-center" variant="primary" type="submit">
          {processing ? <Spinner className="h-6 w-6 text-white" /> : 'Submit'}
        </Button>
      </form>

      {processing && <SpinnerPage />}

      <Navigation />
    </AppLayout>
  )
}
