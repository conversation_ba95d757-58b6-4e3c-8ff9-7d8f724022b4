import { useEffect, useState } from 'react'
import http from '@/utilities/http.js'
import { toast } from 'react-toastify'

export default function useCustomFoods() {
  const [data, setData] = useState([])
  const [loading, setLoading] = useState(false)
  const [processing, setProcessing] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  const fetcher = async () => {
    setLoading(true)

    try {
      await http.get('users/me/custom-foods', { params: { query: searchQuery } }).then(({ data }) => {
        setData(data.data)
        setLoading(false)
      })
    } catch (error) {
      console.log(error)
    }
  }

  const create = async (data) => {
    setProcessing(true)

    try {
      return await http.post('users/me/custom-foods', data).then(() => {
        setProcessing(false)
        fetcher().then()

        return true
      })
    } catch (error) {
      setProcessing(false)
      if (error.status === 422) {
        toast.error(error.response.data.message)
      } else {
        toast.error('Could not create custom food. Please try again later.')
      }
    }

    setProcessing(false)
  }

  const search = (query) => {
    setSearchQuery(query)
  }

  useEffect(() => {
    fetcher().then()
  }, [searchQuery])

  return {
    data,
    loading,
    processing,
    search,
    create
  }
}
