<?php

namespace App\Console\Commands;

use App\Models\Affiliate;
use App\Models\AffiliateBalance;
use App\Models\AffiliateEarning;
use App\Models\ClientCredential;
use App\Models\Coupon;
use App\Models\CustomFood;
use App\Models\Ingredient;
use App\Models\MealPlan;
use App\Models\MealPlanItem;
use App\Models\MealPlanNutrient;
use App\Models\MealPlanRecipe;
use App\Models\Partner;
use App\Models\PartnerMeta;
use App\Models\PartnerStripeAccount;
use App\Models\Recipe;
use App\Models\RecipeCollection;
use App\Models\RecipeCollectionRecipe;
use App\Models\RecipeIngredient;
use App\Models\RecipeIngredientRecipe;
use App\Models\RecipeNutrient;
use App\Models\RecipeNutrientRecipe;
use App\Models\RecipeReview;
use App\Models\RecipeReviewAttachment;
use App\Models\SaluUser;
use App\Models\SpoonacularUser;
use App\Models\StripeProduct;
use App\Models\StripeProductPrice;
use App\Models\StripeUser;
use App\Models\Subscription;
use App\Models\TemporaryCustomRecipe;
use App\Models\TemporaryRecipeFromUrl;
use App\Models\Transaction;
use App\Models\User;
use App\Models\UserMeta;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\Console\Attribute\AsCommand;

#[AsCommand('platform:cleanup', 'Cleanup the platform')]
class PlatformCleanupCommand extends Command
{
    public function handle(): void
    {
        if (app()->environment('production')) {
            if (! $this->confirm('You are in production environment. Do you really wish to continue?')) {
                $this->info('Cleanup aborted.');
                return;
            }
        }

        try {
            $this->info('Backup the database before cleaning up the platform...');
            $res = $this->backup();

            if (! $res) {
                $this->error('Backup failed. Cleanup aborted.');
                return;
            }

            $this->info('Backup completed successfully!');

            $this->info('Cleaning up the platform...');
            $this->cleanupAffiliates();
            $this->cleanupStripeData();
            $this->cleanupShopifyData();
            $this->cleanupSpoonacularData();
            $this->cleanupPartnersAndUsers();
            $this->cleanupAdditionalData();

            $this->info('Platform cleanup completed successfully!');
        } catch (\Exception $e) {
            $this->error('An error occurred while cleaning up the platform: ' . $e->getMessage());
        }
    }

    protected function cleanupAffiliates(): void
    {
        $this->info('Starting to delete all affiliates...');

        AffiliateEarning::query()->truncate();
        AffiliateBalance::query()->truncate();
        Affiliate::query()->truncate();

        $this->info('All affiliates have been deleted successfully!');
    }

    protected function cleanupStripeData(): void
    {
        $this->info('Starting to delete all stripe users...');
        StripeUser::query()->truncate();
        $this->info('All stripe users have been deleted successfully!');

        $this->info('Starting to delete all stripe products...');
        StripeProductPrice::query()->truncate();
        StripeProduct::query()->truncate();
        $this->info('All stripe products have been deleted successfully!');

        $this->info('Starting to delete all stripe coupons...');
        Coupon::query()->truncate();
        $this->info('All stripe coupons have been deleted successfully!');

        $this->info('Starting to delete all stripe subscriptions & transactions...');
        Subscription::query()->truncate();
        Transaction::query()->truncate();
        $this->info('All stripe subscriptions & transactions have been deleted successfully!');
    }

    protected function cleanupSpoonacularData(): void
    {
        $this->info('Start cleanup spoonacular data...');

        Ingredient::query()->truncate();
        RecipeCollectionRecipe::query()->truncate();
        RecipeCollection::query()->truncate();
        RecipeIngredient::query()->truncate();
        RecipeIngredientRecipe::query()->truncate();
        RecipeNutrient::query()->truncate();
        RecipeNutrientRecipe::query()->truncate();
        MealPlanNutrient::query()->truncate();
        MealPlanItem::query()->truncate();
        MealPlanRecipe::query()->truncate();
        MealPlan::query()->truncate();
        RecipeReviewAttachment::query()->forceDelete();
        RecipeReview::query()->forceDelete();
        Recipe::query()->forceDelete();
        SpoonacularUser::query()->truncate();
        TemporaryRecipeFromUrl::query()->truncate();
        TemporaryCustomRecipe::query()->truncate();
        CustomFood::query()->truncate();
        DB::table('meal_plan_nutrient')->truncate();
        DB::table('menu_items')->truncate();

        $this->info('Cleanup spoonacular data successfully!');
    }

    protected function cleanupPartnersAndUsers(): void
    {
        $partners = Partner::query()
            ->with('managers')
            ->whereIn('email', ['<EMAIL>', '<EMAIL>'])
            ->get();

        $exceptUserIds = $partners->pluck('managers')->flatten()->pluck('id')->all();

        Partner::query()
            ->whereNotIn('id', $partners->pluck('id')->all())
            ->delete();

        PartnerMeta::query()->truncate();

        PartnerStripeAccount::query()->truncate();

        User::query()
            ->whereNotIn('id', $exceptUserIds)
            ->where('is_super_admin', false)
            ->delete();

        UserMeta::query()
            ->whereNotIn('user_id', $exceptUserIds)
            ->delete();

        DB::table('user_partner')->where('is_manager', '!=', 1)->delete();
        DB::table('user_social_accounts')->truncate();
        DB::table('user_fcm_tokens')->truncate();
        DB::table('settings')->truncate();
    }

    protected function cleanupAdditionalData(): void
    {
        $this->info('Starting to delete more data...');

        DB::table('password_reset_tokens')->truncate();
        DB::table('user_one_time_passwords')->truncate();
        DB::table('pages')->truncate();
        DB::table('products')->truncate();
        DB::table('oauth_refresh_tokens')->truncate();
        DB::table('oauth_access_tokens')->truncate();
        DB::table('oauth_auth_codes')->truncate();
        DB::table('oauth_clients')->truncate();
        DB::table('oauth_personal_access_clients')->truncate();

        ClientCredential::query()->truncate();
        SaluUser::query()->truncate();
    }

    protected function cleanupShopifyData(): void
    {
        $this->info('Starting to delete all shopify data...');

        DB::table('stripe_users')->truncate();
        DB::table('partner_shopify_stores')->truncate();

        $this->info('All shopify data have been deleted successfully!');
    }

    protected function backup(): bool
    {
        try {
            $database = config('database.connections.mysql.database');
            $username = config('database.connections.mysql.username');
            $password = config('database.connections.mysql.password');
            $host = config('database.connections.mysql.host');
            $port = config('database.connections.mysql.port', '3306');

            $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
            $filename = 'backup_' . $timestamp . '.sql';

            $storage = Storage::disk('local');
            $backupPath = 'backups';
            $fullPath = storage_path("app/{$backupPath}/{$filename}");

            if (!$storage->exists($backupPath)) {
                $storage->makeDirectory($backupPath);
            }

            $tempConfig = tempnam(sys_get_temp_dir(), 'mysqlconfig');
            file_put_contents($tempConfig, sprintf(
                "[client]\nuser=%s\npassword=%s",
                $username,
                $password,
            ));
            chmod($tempConfig, 0600);

            $command = sprintf(
                'mysqldump --defaults-extra-file=%s -h %s -P %s %s > %s',
                $tempConfig,
                $host,
                $port,
                $database,
                $fullPath,
            );

            $this->info('Starting backup...');
            exec($command, $output, $returnVar);

            unlink($tempConfig);

            if ($returnVar === 0) {
                $this->info('Database backup completed successfully!');
                $this->info('Backup saved to: ' . $fullPath);
                return true;
            }

            $this->error('Database backup failed!');
            $this->error('Command output: ' . implode("\n", $output));

            return false;

        } catch (\Exception $e) {
            $this->error('An error occurred: ' . $e->getMessage());

            return false;
        }
    }
}
