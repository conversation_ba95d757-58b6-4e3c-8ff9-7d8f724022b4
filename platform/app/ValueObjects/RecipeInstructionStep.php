<?php

namespace App\ValueObjects;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Arr;

class RecipeInstructionStep implements Arrayable
{
    public function __construct(
        readonly public int $number,
        readonly public string $title,
        readonly public ?array $ingredients,
        readonly public ?array $equipment,
    ) {}

    public static function createFromArray(array $data): static
    {
        $ingredients = $data['ingredients']
            ? array_map(fn(array $ingredient) => RecipeIngredient::createFromArray($ingredient), $data['ingredients'])
            : [];

        $equipment = $data['equipment']
            ? array_map(fn(array $item) => RecipeEquipment::createFromArray($item), $data['equipment'])
            : [];

        return new static(
            $data['number'],
            Arr::get($data, 'title', Arr::get($data, 'step')),
            $ingredients,
            $equipment,
        );
    }

    public function toArray(): array
    {
        return [
            'number' => $this->number,
            'title' => $this->title,
            'ingredients' => $this->ingredients,
            'equipment' => $this->equipment,
        ];
    }
}
