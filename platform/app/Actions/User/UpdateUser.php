<?php

namespace App\Actions\User;

use App\Enums\UserGender;
use App\Models\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

class UpdateUser
{
    public function update(User $user, array $inputs): User
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user)],
            'date_of_birth' => ['nullable', 'string', 'date_format:Y-m-d'],
            'weight' => ['nullable', 'numeric', 'min:0'],
            'height' => ['nullable', 'numeric', 'min:0'],
            'bio' => ['nullable', 'string', 'max:1000'],
            'gender' => ['nullable', 'string', new Enum(UserGender::class)],
            'profile_photo_path' => ['nullable', 'string'],
        ];

        if (Arr::get($inputs, 'is_change_password')) {
            $rules['password'] = ['required', 'string', 'min:8', 'confirmed'];
        }

        if ($profilePhoto = Arr::get($inputs, 'profile_photo_path')) {
            $user->profile_photo_path = $profilePhoto;
        }

        Validator::make($inputs, $rules)->validate();

        $user->name = $inputs['name'];
        $user->email = $inputs['email'];
        $user->date_of_birth = Arr::get($inputs, 'date_of_birth');
        $user->bio = Arr::get($inputs, 'bio');
        $user->gender = Arr::get($inputs, 'gender');

        if (Arr::get($inputs, 'is_change_password')) {
            $user->password = Hash::make(Arr::get($inputs, 'password'));
        }

        $user->save();

        if (Arr::get($inputs, 'weight') || Arr::get($inputs, 'height')) {
            $user->saluUser()->updateOrCreate(
                ['user_id' => $user->id],
                [
                    'weight' => Arr::get($inputs, 'weight'),
                    'height' => Arr::get($inputs, 'height'),
                ],
            );
        }

        return $user;
    }
}
