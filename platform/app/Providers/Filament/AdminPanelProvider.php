<?php

namespace App\Providers\Filament;

use App\Filament\Admin\Widgets\StripeStatsOverviewWidget;
use App\Support\Facades\Core;
use Filament\Facades\Filament;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Saade\FilamentLaravelLog\FilamentLaravelLogPlugin;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->path(Core::adminPath())
            ->domain(Core::adminDomain())
            ->login()
            ->profile()
            ->passwordReset()
            ->colors([
                'primary' => Color::Blue,
            ])
            ->discoverResources(
                in: app_path('Filament/Admin/Resources'),
                for: 'App\\Filament\\Admin\\Resources',
            )
            ->discoverPages(
                in: app_path('Filament/Admin/Pages'),
                for: 'App\\Filament\\Admin\\Pages',
            )
            ->discoverClusters(
                in: app_path('Filament/Admin/Clusters'),
                for: 'App\\Filament\\Admin\\Clusters',
            )
            ->discoverPages(
                in: app_path('Filament/Admin/Clusters/Pages'),
                for: 'App\\Filament\\Admin\\Clusters\\Pages',
            )
            ->pages([
                \App\Filament\Admin\Pages\Dashboard::class,
            ])
            ->widgets([
                Widgets\AccountWidget::class,
                Widgets\FilamentInfoWidget::class,
                StripeStatsOverviewWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->navigationGroups([
                'Content',
                'Subscriptions',
                'Affiliates',
                'Systems',
            ])
            ->plugin(
                FilamentLaravelLogPlugin::make()
                    ->navigationSort(1000)
                    ->navigationLabel(__('System Logs'))
                    ->navigationGroup('Systems')
                    ->authorize(function () {
                        $user = Filament::auth()->user();

                        if (! $user) {
                            return false;
                        }

                        if ($user->is_super_admin) {
                            return true;
                        }

                        return $user->hasPermissionTo('admin:system_logs');
                    }),
            )
            ->databaseNotifications();
    }
}
