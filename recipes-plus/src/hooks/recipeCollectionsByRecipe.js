import { useEffect, useState } from 'react'
import http from '@/utilities/http.js'
import { toast } from 'react-toastify'

export default function useRecipeCollectionsByRecipe(recipeId) {
  const [data, setData] = useState([])
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [currentCollections, setCurrentCollections] = useState([])

  const fetch = async () => {
    setLoading(true)

    try {
      await http.get(`recipes/${recipeId}/collections`).then(({ data }) => {
        setData(data.data)
        setCurrentCollections(['default', ...data.data.map((collection) => collection.id)])
      })
    } catch (error) {
      console.error('Error fetching recipe collections data.')
    }

    setLoading(false)
  }

  const toggleCollection = async (collectionId) => {
    if (currentCollections.includes(collectionId)) {
      setCurrentCollections(currentCollections.filter((id) => id !== collectionId))
    } else {
      setCurrentCollections([...currentCollections, collectionId])
    }
  }

  const saveCollections = async () => {
    setProcessing(true)

    try {
      await http.post(`recipes/${recipeId}/collections`, { collection_ids: currentCollections }).then(({ data }) => {
        setData(data.data)
        toast.success('Recipe saved to collection successfully!')
      })
    } catch (error) {
      toast.error('Could not save recipe to collection. Please try again later.')
    }

    setProcessing(false)
  }

  useEffect(() => {
    fetch().then()
  }, [])

  return {
    data,
    currentCollections,
    loading,
    processing,
    toggleCollection,
    saveCollections
  }
}
