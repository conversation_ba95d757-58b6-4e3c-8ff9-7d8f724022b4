import { forwardRef, useEffect, useState } from 'react'
import { Button } from '@/components/Button'
import PropTypes from 'prop-types'
import { featureNotAvailable } from '@/utilities'

const RecommendationListItem = forwardRef((props, ref) => {
  const { className, recommendation, recommendationUrls, toggleTooltip, ...attributes } = props

  const [buyUrl, setBuyUrl] = useState()

  let cx = className || ''

  cx += ' cursor-pointer bg-white p-4 rounded justify-between gap-2 mb-2'

  const handleTooltip = () => {
    toggleTooltip(recommendation)
  }

  const gotoShop = () => {
    window.location.href = buyUrl
  }

  useEffect(() => {
    if (recommendationUrls) {
      setBuyUrl(recommendationUrls[recommendation.recommendation_id] || undefined)
    }
  }, [])

  return (
    recommendation && (
      <div ref={ref} className={cx} {...attributes}>
        <div className="relative mb-1 max-w-[98%] text-sm font-medium">
          {recommendation.label}
          {/* {recommendation.how !== undefined && (
            <Button onClick={handleTooltip} variant="link" className="absolute !p-0 no-underline">
              <span className="ms-1 flex h-[20px] w-[20px] items-center justify-center rounded-full border text-sm text-secondary">
                <span className="text-sm">?</span>
              </span>
            </Button>
          )} */}
        </div>
        <div className="flex items-start justify-between gap-2">
          {recommendation.dose_units !== '' && (
            <div className="text-xs text-primary text-opacity-80">
              {parseFloat(recommendation.dose_amount).toFixed(2)} {recommendation.dose_units}{' '}
              {recommendation.dose_interval}
            </div>
          )}
          <div className="relative flex h-6 w-20 shrink-0 items-center justify-center rounded border border-slate-200">
            {buyUrl ? (
              <Button variant="link" className="text-xs !font-normal text-secondary no-underline" onClick={gotoShop}>
                Buy Now
              </Button>
            ) : (
              <Button
                variant="link"
                className="text-xs !font-normal text-secondary no-underline"
                onClick={handleTooltip}
              >
                Learn More
              </Button>
            )}
          </div>
        </div>
      </div>
    )
  )
})

RecommendationListItem.displayName = 'RecommendationListItem'

RecommendationListItem.propTypes = {
  className: PropTypes.string,
  recommendation: PropTypes.object,
  recommendationUrls: PropTypes.array,
  toggleTooltip: PropTypes.func
}

export { RecommendationListItem }
