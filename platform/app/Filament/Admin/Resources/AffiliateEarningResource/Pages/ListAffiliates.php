<?php

namespace App\Filament\Admin\Resources\AffiliateEarningResource\Pages;

use App\Filament\Admin\Resources\AffiliateEarningResource;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Resources\Pages\ListRecords;

class ListAffiliates extends ListRecords
{
    use ExposesTableToWidgets;

    protected static string $resource = AffiliateEarningResource::class;

    protected function getHeaderWidgets(): array
    {
        return AffiliateEarningResource::getWidgets();
    }
}
