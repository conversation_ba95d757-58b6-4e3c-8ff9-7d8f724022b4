<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::table('meal_plans', function (Blueprint $table) {
            $table->float('cost')->default(0)->after('partner_id')->nullable();
        });

        Schema::create('meal_plan_nutrients', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
        });

        Schema::table('meal_plan_recipe', function (Blueprint $table) {
            $table->unsignedInteger('slot')->nullable();
            $table->unsignedInteger('position')->nullable();
            $table->unsignedInteger('servings')->nullable();
        });

        Schema::table('meal_plan_nutrient', function (Blueprint $table) {
            $table->foreignId('meal_plan_nutrient_id');
            $table->dropColumn('nutrient_id');
            $table->float('amount');
            $table->float('percent_of_daily_needs');
            $table->string('unit')->nullable();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('meal_plan_nutrients');

        Schema::table('meal_plans', function (Blueprint $table) {
            $table->dropColumn('cost');
        });

        Schema::table('meal_plan_recipe', function (Blueprint $table) {
            $table->dropColumn('slot');
            $table->dropColumn('position');
            $table->dropColumn('servings');
        });

        Schema::table('meal_plan_nutrient', function (Blueprint $table) {
            $table->dropColumn('meal_plan_nutrient_id');
            $table->foreignId('nutrient_id');
            $table->dropColumn('amount');
            $table->dropColumn('percent_of_daily_needs');
            $table->dropColumn('unit');
        });
    }
};
