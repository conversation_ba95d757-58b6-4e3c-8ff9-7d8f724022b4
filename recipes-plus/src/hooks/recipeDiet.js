import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import http from '@/utilities/http'
import useAuthStore from '@/stores/auth'

export default function useRecipeDiet() {
  const [data, setData] = useState([])
  const [loading, setLoading] = useState(true)
  const { accessToken } = useAuthStore()

  const getDiet = async () => {
    setLoading(true)

    return await http
      .get('recipes/diet', { headers: { Authorization: `Bearer ${accessToken}` } })
      .then(({ data }) => {
        setData(data.data)
        setLoading(false)

        return data.data
      })
      .catch(() => {
        toast.error('Could not fetch diet. Please try again later.')
      })
  }

  useEffect(() => {
    getDiet().then()
  }, [])

  return {
    loading,
    data
  }
}
