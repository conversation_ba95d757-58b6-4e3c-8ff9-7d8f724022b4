import { forwardRef } from 'react'
import PropTypes from 'prop-types'

const PageHeaderTitle = forwardRef((props, ref) => {
  const { children, className, ...attributes } = props

  let cx = className || ''

  cx += 'w-full truncate p-2 text-2xl font-bold text-center'

  return (
    <h2 ref={ref} className={cx} {...attributes}>
      {children}
    </h2>
  )
})

PageHeaderTitle.displayName = 'PageHeaderTitle'

PageHeaderTitle.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string
}

export { PageHeaderTitle }
