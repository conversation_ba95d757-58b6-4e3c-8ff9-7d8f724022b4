import { forwardRef } from 'react'

const SupporterIcon = forwardRef((props, ref) => {
  return (
    <svg ref={ref} {...props} viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M7.824 6.437C8.089 6.437 8.352 6.333 8.55 6.125C9.291 5.344 10.613 5.315 11.385 6.06C11.783 6.444 12.416 6.432 12.799 6.035C13.183 5.637 13.171 5.004 12.774 4.621C12.023 3.897 11.039 3.5 10 3.5C8.909 3.5 7.852 3.955 7.099 4.748C6.719 5.148 6.736 5.781 7.136 6.162C7.329 6.346 7.577 6.437 7.824 6.437Z"
        fill="white"
      />
      <path
        d="M17 7.5C17 3.641 13.859 0.5 10 0.5C6.141 0.5 3 3.641 3 7.5C1.346 7.5 0 8.846 0 10.5V12.5C0 14.154 1.346 15.5 3 15.5H4C4.553 15.5 5 15.053 5 14.5V7.5C5 4.743 7.243 2.5 10 2.5C12.757 2.5 15 4.743 15 7.5V14.583C15 16.191 13.691 17.5 12.083 17.5H12C12 16.397 11.103 15.5 10 15.5H9C7.897 15.5 7 16.397 7 17.5V18.5C7 19.603 7.897 20.5 9 20.5H10C10.737 20.5 11.375 20.095 11.722 19.5H12.083C14.48 19.5 16.476 17.773 16.907 15.5H17C18.654 15.5 20 14.154 20 12.5V10.5C20 8.846 18.654 7.5 17 7.5ZM3 13.5C2.448 13.5 2 13.052 2 12.5V10.5C2 9.948 2.448 9.5 3 9.5V13.5ZM10 18.5H9V17.5H10V18.5ZM18 12.5C18 13.052 17.552 13.5 17 13.5V9.5C17.552 9.5 18 9.948 18 10.5V12.5Z"
        fill="white"
      />
    </svg>
  )
})

SupporterIcon.displayName = 'SupporterIcon'

export { SupporterIcon }
