<?php

namespace App\Listeners;

use App\Events\ShopifyDisconnected;
use App\Models\PartnerShopifyStore;
use App\Models\ShopifyUser;
use App\Support\Facades\ShopifyHelper;

class DeleteAllOnShopifyDisconnectedListener
{
    public function handle(ShopifyDisconnected $event): void
    {
        if (! ShopifyHelper::canDisconnect()) {
            return;
        }

        ShopifyUser::query()->truncate();
        PartnerShopifyStore::query()->truncate();
    }
}
