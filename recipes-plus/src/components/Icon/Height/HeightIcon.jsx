import { forwardRef } from 'react'
import PropTypes from 'prop-types'

const HeightIcon = forwardRef((props, ref) => {
  const { className } = props

  let cx = className || ''

  cx += ' icon icon-tabler icons-tabler-outline icon-tabler-plus'

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      ref={ref}
      {...props}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={cx}
    >
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 20h-6a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h6" />
      <path d="M18 14v7" />
      <path d="M18 3v7" />
      <path d="M15 18l3 3l3 -3" />
      <path d="M15 6l3 -3l3 3" />
    </svg>
  )
})

HeightIcon.displayName = 'HeightIcon'

HeightIcon.propTypes = {
  className: PropTypes.string
}

export { HeightIcon }
