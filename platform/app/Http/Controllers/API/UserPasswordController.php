<?php

namespace App\Http\Controllers\API;

use App\Actions\User\UpdateUserPassword;
use App\Http\Controllers\APIController;
use App\Http\Requests\API\UserPasswordUpdateRequest;
use App\Http\Responses\Errors\ForbiddenResponse;
use App\Http\Responses\JsonResponse;

class UserPasswordController extends APIController
{
    public function __invoke(
        UserPasswordUpdateRequest $request,
        UpdateUserPassword $updateUserPassword,
        int|string $user,
    ): JsonResponse {
        if ($user !== 'me') {
            return ForbiddenResponse::make();
        }

        $updateUserPassword->update($request->user(), $request->input('password'));

        return new JsonResponse([
            'message' => 'Updated.',
        ]);
    }
}
