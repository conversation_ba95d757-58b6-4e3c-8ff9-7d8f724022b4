<?php

namespace App\Services;

use App\Exceptions\SalugeniusUserKeyRequiredException;
use App\Models\SaluUser;
use App\Salugenius\SalugeniusData;
use App\Salugenius\SalugeniusField;
use App\Support\Helper;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class Salugenius
{
    public const URL = 'https://services.salugenius.com/services.php';

    public const CLIENT_ID = 'vga';

    public int $defaultGroupMemberId = 3;

    protected string $userKey;

    public function withUserKey(string $userKey): static
    {
        $this->userKey = $userKey;

        return $this;
    }

    public function withoutUserKey(): static
    {
        return $this->withUserKey('');
    }

    protected function createRequest(): PendingRequest
    {
        if (! isset($this->userKey)) {
            throw new SalugeniusUserKeyRequiredException();
        }

        return Http::baseUrl(self::URL)
            ->asForm()
            ->when(
                $this->userKey,
                fn(PendingRequest $request)
                => $request->withQueryParameters(['userkey' => $this->userKey]),
            );
    }

    public function send(SalugeniusData $data)
    {
        return $this
            ->createRequest()
            ->post('', ['request' => $data->toXML()]);
    }

    public function setDemographics(string $saluId, array $attributes = [])
    {
        $response = $this
            ->withUserKey($saluId)
            ->send(
                SalugeniusData::make()
                    ->addField(
                        SalugeniusField::make('set_demographics')
                            ->addAttributes($attributes),
                    ),
            );

        $rawXml = $response->body();
        $data = $this->parseToArray($rawXml);

        return $data;
    }

    public function setUser(array $attributes = []): array
    {
        $response = $this->withoutUserKey()
            ->send(
                SalugeniusData::make()
                    ->addField(
                        SalugeniusField::make('user_set')
                            ->addAttributes($attributes),
                    ),
            );

        if (! $response->ok()) {
            return [];
        }

        $rawXml = $response->body();

        $data = $this->parseToArray($rawXml);

        if (! Arr::has($data, 'user_set.@attributes')) {
            return [];
        }

        return $data['user_set']['@attributes'];
    }

    public function percentageCalculation(string $saluId, array $swims): array
    {
        $completions = [];

        if (! $swims) {
            return $completions;
        }

        foreach ($swims as $key => $swim) {
            $laneNumber = $key;

            if (! is_numeric($key)) {
                continue;
            }

            $attributes = ['lane' => $key, 'client_key' => $saluId, 'answered' => true];

            $response = $this
                ->withUserKey($saluId)
                ->send(
                    SalugeniusData::make()
                        ->addField(SalugeniusField::make('get_inputs')->addAttributes($attributes)),
                );

            if (! $response->ok()) {
                return [];
            }

            $data = $this->parseToArray($response->body());

            $inputList = $data['get_inputs']['input'];
            $groupArray = [];
            $totalQuestions = 0;
            $answeredQuestions = 0;
            foreach ($inputList as $i => $ir) {
                $input = $ir['@attributes'];
                $input_id = $input['input_id'];
                $group_id = $input['group_id'];
                if ($group_id == 0) {
                    $totalQuestions++;
                    if ($input['used'] == 1) {
                        $answeredQuestions++;
                    }
                } else {
                    if (! in_array($group_id, $groupArray) && $group_id > 0) {
                        $groupArray[] = $group_id;
                        $totalQuestions++;
                        if ($input['used'] == 1) {
                            $answeredQuestions++;
                        }
                    }
                }
            }
            $pct = ($totalQuestions > 0 ? round(($answeredQuestions / $totalQuestions) * 100) : 0);
            if ($pct >= 90) {
                $pct = 100;
            }
            $completions[$laneNumber] = [
                'questions' => $totalQuestions,
                'answered' => $answeredQuestions,
                'completion' => $pct,
                'label' => $swim['label'],
            ];
        }

        return $completions;
    }

    public function getHealthQuestionsByCategory(string $saluId, array $data): array
    {
        $swims = Helper::getSwims();

        $response = $this
            ->withUserKey($saluId)
            ->send(
                SalugeniusData::make()
                    ->addField(
                        SalugeniusField::make('get_system')
                        ->addAttributes(['system_id' => '25']),
                    ),
            );

        if (! $response->ok()) {
            return [];
        }

        $systemData = $this->parseToArray($response->body());

        $completions = $this->percentageCalculation($saluId, $swims);

        $result = [];
        $result['pagetitle'] = Arr::get($swims, $data['lanelist'])['label'];
        //Questions follows

        $questions = $data['lanelist'] === 'smart' ? $this->getSmartQuestions($saluId) : $this->getQuestions($saluId, $data);

        $result['main'] = $data['lanelist'] !== 'smart' ? $questions[0] : null;
        $result['input'] = $data['lanelist'] !== 'smart' ? $questions[1] : $questions;

        if (! $data) {
            $result['swim'] = 'smart';
        }

        $result['swims'] = $swims;
        $result['completions'] = $completions;
        $result['healthscore'] = round((float) Arr::get($systemData, 'get_system.@attributes.p') * 100);

        // Completion rate
        $totalQuestions = 0;
        $totalAnswers = 0;

        foreach (array_keys(Arr::except(Helper::getSwims(), ['smart', 'rx'])) as $value) {
            $totalQuestions = $totalQuestions + (int) $completions[$value]['questions'];
            $totalAnswers = $totalAnswers + (int) $completions[$value]['answered'];
        }

        $result['health_questionnaire_completion'] = (int) $totalQuestions ? (int) (((int) $totalAnswers / (int) $totalQuestions) * 100) : 0;

        if ($data &&  ! in_array($data['lanelist'], ['smart', 'rx'])) {
            $result['completions_rate'] = $completions[$data['lanelist']]['completion'];
        }

        if ($result['healthscore'] < 30) {
            $result['healthscorecolor'] = '#ab0000';
        }
        if ($result['healthscore'] >= 30 && $result['healthscore'] < 80) {
            $result['healthscorecolor'] = '#ffdc00';
        }
        if ($result['healthscore'] >= 80) {
            $result['healthscorecolor'] = '#42b800';
        }

        $risk = '';

        if ($result['healthscore'] < 30) {
            $risk = 'High Risk';
        }

        if ($result['healthscore'] > 80) {
            $risk = 'Low Risk';
        }

        $result['risk'] = $risk;

        return $result;
    }

    public function getQuestions(string $saluId, array $data): array
    {
        $laneList = Arr::get($data, 'lanelist');

        if (! $laneList) {
            return [];
        }

        if ($laneList === 'rx') {
            $laneList = '78, 79';
        }

        if (! is_array($laneList)) {
            $laneNumbers = explode(',', $laneList);
        } else {
            $laneNumbers = $laneList;
        }

        $getLaneData = SalugeniusData::make();
        $getInputData = SalugeniusData::make();

        foreach ($laneNumbers as $laneNumber) {
            if ($laneNumber > 0) {
                $request = $this
                    ->withUserKey($saluId);

                $getLaneData->addField(SalugeniusField::make('get_lane')->addAttributes(['lane_id' => $laneNumber]));
                $getInputData->addField(SalugeniusField::make('get_inputs')->addAttributes(['lane' => $laneNumber, 'client_key' => $saluId ]));
            }
        }

        $getLaneResponse = $request->send($getLaneData);

        $getInputResponse = $request->send($getInputData);

        return [$this->parseToArray($getLaneResponse->body()), $this->parseToArray($getInputResponse->body())];
    }

    public function getSmartQuestions($saluId): array
    {
        $request = $this
            ->withUserKey($saluId);

        $response = $request
            ->send(
                SalugeniusData::make()
                    ->addField(
                        SalugeniusField::make('get_smartquestions')
                    )
            );

        return $this->parseToArray($response->body());
    }

    public function saveHealthQuestions(string $saluId, array $data): array
    {
        $saluData = SalugeniusData::make();

        foreach ($data as $key => $value) {
            if (str_contains($key, 'INPUT')) {
                $attributes = [
                    'input_id' => Str::of($key)->after('INPUT_')->toString(),
                    'value' => $value,
                ];

                $saluData->addField(SalugeniusField::make('set_input')->addAttributes($attributes));
            }

            if (str_contains($key, 'GROUP')) {
                $attributes = [
                    'group_id' => Str::of($key)->after('GROUP_')->toString(),
                    'input_id' => $value,
                    'value' => 1,
                ];

                $saluData->addField(SalugeniusField::make('set_group')->addAttributes($attributes));
            }
        }

        $response = $this
            ->withUserKey($saluId)
            ->send($saluData);

        if (! $response->ok()) {
            return [];
        }

        return $this->parseToArray($response->body());
    }

    public function getRecommendations(string $saluId, array $data): array
    {
        $response = $this
            ->withUserKey($saluId)
            ->send(
                SalugeniusData::make()
                    ->addField(SalugeniusField::make('get_recommendations')),
            );

        if (! $response->ok()) {
            return [];
        }

        $xmlData = simplexml_load_string($response->body());

        if (! $xmlData->recommendations?->recommendation) {
            return [];
        }

        $result = SaluUser::query()->where('salu_id', '=', $saluId)->first();

        if (! $result) {
            return [];
        }

        $userId = $result->user_id;

        $nsupct = $lsct = $dcct = $fcct = $dpct = $etct = 0;

        $nsupctm = $lsctm = $dcctm = $fcctm = $dpctm = $etctm = 0;

        $multi = [];

        $newfeatureson = false;

        $thresholds = [
            'topical' => 40,
            'nutritional' => 40,
            'botanical' => 40,
            'lifestyle' => 20,
            'nutrient' => 20,
            'foodchanges' => 40,
            'dietary' => 20,
            'environmental' => 20,
        ];

        foreach ($xmlData->recommendations->recommendation as $recommendation) {
            $recommendation['int_desc'] = $interventions[(int) $recommendation['intervention']]['description'] ?? '';
            $recommendation['int_label'] = $interventions[(int) $recommendation['intervention']]['label'] ?? '';

            if ($recommendation['category'] == 'Nutritional supplementation'
                || (! Str::startsWith($saluId, static::CLIENT_ID) && $recommendation['category'] == 'Botanical Medicine')) {
                if ($recommendation['intervention'] != '') {
                    if ($newfeatureson) {
                        if ($recommendation['p'] >= 40 && $nsupct < 3) {
                            $recommendation['pri'] = 'high';
                            $nutsup[(int) $recommendation['recommendation_id']] = $recommendation;
                        } elseif ($recommendation['p'] >= 40 && $nsupct >= 3 && $nsupct < 6 && $nsupctm < 3) {
                            $recommendation['pri'] = 'med';
                            $nutsup[(int) $recommendation['recommendation_id']] = $recommendation;
                            $nsupct++;
                        } elseif (($recommendation['p'] >= 20 && $recommendation['p'] < 40) && $nsupct < 6 && $nsupctm < 3) {
                            $recommendation['pri'] = 'med';
                            $nutsup[(int) $recommendation['recommendation_id']] = $recommendation;
                            $nsupct++;
                        } elseif ($nsupct < 6 && $nsupct < 3) {
                            $recommendation['pri'] = 'med';
                            $nutsup[(int) $recommendation['recommendation_id']] = $recommendation;
                            $nsupct++;
                        } elseif (($nsupct >= 3 && $nsupct < 6)) {
                            $recommendation['pri'] = 'low';
                            $nutsup[(int) $recommendation['recommendation_id']] = $recommendation;
                        }
                        if ($recommendation['recommendation_id'] == 421) {
                            $multi[0] = $recommendation;
                        }
                        $nsupct++;
                    } else {
                        if ($recommendation['category'] == 'Botanical Medicine'
                            && $recommendation['p'] >= $thresholds['botanical']) {
                            $nutsup[(int) $recommendation['recommendation_id']] = $recommendation;
                        }
                        if ($recommendation['recommendation_id'] == 421) {
                            $multi[0] = $recommendation;
                        }
                        if (! Str::startsWith($userId, static::CLIENT_ID)
                            && $recommendation['category'] == 'Nutritional supplementation'
                            && $recommendation['p'] >= $thresholds['nutritional']) {
                            $nutsup[(int) $recommendation['recommendation_id']] = $recommendation;
                        }
                    }
                }
            }
            if ($recommendation['category'] == 'Lifestyle interventions') {
                if ($recommendation['intervention'] != '') {
                    if ($newfeatureson) {
                        if ($recommendation['p'] >= 40 && $lsct < 3) {
                            $recommendation['pri'] = 'high';
                            $lifestyles[] = $recommendation;
                        } elseif ($recommendation['p'] >= 40 && $lsct >= 3 && $lsct < 6 && $lsctm < 3) {
                            $recommendation['pri'] = 'med';
                            $lifestyles[] = $recommendation;
                            $lsctm++;
                        } elseif (($recommendation['p'] >= 20 && $recommendation['p'] < 40) && $lsct < 6 && $lsctm < 3) {
                            $recommendation['pri'] = 'med';
                            $lifestyles[] = $recommendation;
                            $lsctm++;
                        } elseif ($lsct < 6 && $lsctm < 3) {
                            $recommendation['pri'] = 'med';
                            $lifestyles[] = $recommendation;
                            $lsctm++;
                        } elseif (($lsct < 6 && $lsctm >= 3)) {
                            $recommendation['pri'] = 'low';
                        }
                        $lsct++;
                    } else {
                        if ($recommendation['p'] >= $thresholds['lifestyle']) {
                            $lifestyles[] = $recommendation;
                        }
                    }
                }
            }
            if ($recommendation['category'] == 'Nutrient Changes in the Diet') {
                if ($recommendation['intervention'] != '') {
                    if ($newfeatureson) {
                        if ($recommendation['p'] >= 40 && $dcct < 3) {
                            $recommendation['pri'] = 'high';
                            $dietchanges[] = $recommendation;
                        } elseif ($recommendation['p'] >= 40 && $dcct >= 3 && $dcct < 6 && $dcctm < 3) {
                            $recommendation['pri'] = 'med';
                            $dietchanges[] = $recommendation;
                            $dcctm++;
                        } elseif (($recommendation['p'] >= 20 && $recommendation['p'] < 40) && $dcct < 6 && $dcctm < 3) {
                            $recommendation['pri'] = 'med';
                            $dietchanges[] = $recommendation;
                            $dcctm++;
                        } elseif ($dcct < 6 && $dcctm < 3) {
                            $recommendation['pri'] = 'med';
                            $dietchanges[] = $recommendation;
                            $dcctm++;
                        } elseif (($dcctm >= 3 && $dcct < 6)) {
                            $recommendation['pri'] = 'low';
                            $dietchanges[] = $recommendation;
                        }
                        $dcct++;
                    } else {
                        if ($recommendation['p'] >= $thresholds['nutrient']) {
                            $dietchanges[] = $recommendation;
                        }
                    }
                }
            }
            if ($recommendation['category'] == 'Food Changes') {
                if ($recommendation['intervention'] != '') {
                    if ($newfeatureson) {
                        if ($recommendation['p'] >= 40 && $fcct < 3) {
                            $recommendation['pri'] = 'high';
                            $diets[] = $recommendation;
                        } elseif ($recommendation['p'] >= 40 && $fcct >= 3 && $fcct < 6 && $fcctm < 3) {
                            $recommendation['pri'] = 'med';
                            $diets[] = $recommendation;
                            $fcctm++;
                        } elseif (($recommendation['p'] >= 20 && $recommendation['p'] < 40) && $fcct < 6 && $fcctm < 3) {
                            $recommendation['pri'] = 'med';
                            $diets[] = $recommendation;
                            $fcctm++;
                        } elseif ($fcct < 6 && $fcctm < 3) {
                            $recommendation['pri'] = 'med';
                            $diets[] = $recommendation;
                            $fcctm++;
                        } elseif ($fcct < 6 && $fcctm >= 3) {
                            $recommendation['pri'] = 'low';
                            $diets[] = $recommendation;
                        }
                        $fcct++;
                    } else {
                        if ($recommendation['p'] >= $thresholds['foodchanges']) {
                            $diets[] = $recommendation;
                        }
                    }
                }
            }

            if ($recommendation['category'] == 'Dietary Plans') {
                if ($recommendation['intervention'] != '') {
                    if ($newfeatureson) {
                        if ($recommendation['p'] >= 40 && $dpct < 3) {
                            $recommendation['pri'] = 'high';
                            $dietplans[] = $recommendation;
                        } elseif ($recommendation['p'] >= 40 && $dpct >= 3 && $dpct < 6 && $dpctm < 3) {
                            $recommendation['pri'] = 'med';
                            $dietplans[] = $recommendation;
                            $dpctm++;
                        } elseif (($recommendation['p'] >= 20 && $recommendation['p'] < 40) && $dpct < 6 && $dpctm < 3) {
                            $recommendation['pri'] = 'med';
                            $dietplans[] = $recommendation;
                            $dpctm++;
                        } elseif ($dpct < 6 && $dpctm < 3) {
                            $recommendation['pri'] = 'med';
                            $dietplans[] = $recommendation;
                            $dpctm++;
                        } elseif (($dpct < 6 && $dpctm >= 3)) {
                            $recommendation['pri'] = 'low';
                            $dietplans[] = $recommendation;
                        }
                        $dpct++;
                    } else {
                        if ($recommendation['p'] >= $thresholds['dietary']) {
                            $dietplans[] = $recommendation;
                        }
                    }
                }
            }

            if ($recommendation['category'] == 'Environmental Toxin Reduction') {
                if ($newfeatureson) {
                    if ($recommendation['p'] >= 40 && $etct < 3) {
                        $recommendation['pri'] = 'high';
                        $envtox[] = $recommendation;
                    } elseif ($recommendation['p'] >= 40 && $etct >= 3 && $etct < 6 && $etctm < 3) {
                        $recommendation['pri'] = 'med';
                        $envtox[] = $recommendation;
                        $etctm++;
                    } elseif (($recommendation['p'] >= 20 && $recommendation['p'] < 40) && $etct < 6 && $etctm < 3) {
                        $recommendation['pri'] = 'med';
                        $envtox[] = $recommendation;
                        $etctm++;
                    } elseif ($etct < 6 && $etctm < 3) {
                        $recommendation['pri'] = 'med';
                        $envtox[] = $recommendation;
                        $etctm++;
                    } elseif (($etctm >= 3 && $etct < 6)) {
                        $recommendation['pri'] = 'low';
                        $envtox[] = $recommendation;
                    }
                    $etct++;
                } else {
                    if ($recommendation['p'] >= $thresholds['environmental']) {
                        $envtox[] = $recommendation;
                    }
                }
            }

            $recommendations[] = (array) $recommendation;
            $allrecs[] = (array) $recommendation;
        } // foreach

        $excluded = ($data['interventions']['excluded'] ?? []);
        $alltherecs = [];
        $allcats = [];
        $badcats = [];

        foreach ($allrecs as $value) {
            if (! in_array((int) $value['@attributes']['category_id'], $badcats)) {
                if (! in_array(
                    (int) $value['@attributes']['recommendation_id'],
                    $excluded,
                )) {
                    $value['@attributes']['excluded'] = 1;
                    $newrecs[] = $value['@attributes'];
                }
                $alltherecs[] = $value['@attributes'];
                if (! in_array($value['@attributes']['category'], $allcats)) {
                    $cleancat = strtolower($value['@attributes']['category']);
                    $cleancat = preg_replace('/\s/', '_', $cleancat);
                    $allcats[$cleancat] = $value['@attributes']['category'];
                }
            }
        }

        $data['rec_urls'] = [];
        usort($newrecs, [Helper::class, 'psort']);
        usort($alltherecs, [Helper::class, 'psort']);
        $recArr = [];

        foreach ($newrecs as $rec) {
            if ($rec['p'] > 30 && (isset($rec['how']) && $rec['how'] != '')) {
                $recArr[] = $rec;
            }
        }

        $data['allcats'] = $allcats;
        $data['allrecs'] = $alltherecs;
        $data['newrecs'] = $recArr;
        $data['show_dose'] = true;

        return $data;
    }

    protected function parseToArray(?string $xml): array
    {
        if (! $xml) {
            return [];
        }

        $data = simplexml_load_string($xml);

        if (! $data) {
            return [];
        }

        $data = json_encode($data);

        if (! is_string($data)) {
            return [];
        }

        return json_decode($data, true);
    }
}
