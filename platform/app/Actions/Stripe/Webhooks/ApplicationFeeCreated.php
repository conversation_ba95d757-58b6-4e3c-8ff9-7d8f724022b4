<?php

namespace App\Actions\Stripe\Webhooks;

use App\Models\Partner;
use Illuminate\Support\Arr;

class ApplicationFeeCreated
{
    public function handle(array $data): void
    {
        $dataType = Arr::get($data, 'object');

        if ($dataType !== 'application_fee') {
            return;
        }

        $data['subscription_id'] = Arr::get($data, 'subscription');

        $partner = Partner::query()
            ->whereHas('stripeAccount', function ($query) use ($data) {
                $query->where('stripe_id', Arr::get($data, 'account'));
            })
            ->first();

        if (! $partner) {
            return;
        }
    }
}
