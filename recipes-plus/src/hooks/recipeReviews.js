import { useState } from 'react'
import http from '@/utilities/http.js'
import useAuthStore from '@/stores/auth.js'
import { toast } from 'react-toastify'

export default function useRecipeReviews(id, guest = false) {
  const [data, setData] = useState()
  const [loading, setLoading] = useState(true)
  const { accessToken } = useAuthStore()

  const fetcher = async () => {
    setLoading(true)

    let httpRequest = http.get(`recipes/${id}/reviews`, {
      headers: { Authorization: `Bearer ${accessToken}` }
    })

    if (guest) {
      httpRequest = http.get(`guest/recipes/${id}/reviews`)
    }

    try {
      await httpRequest.then(({ data }) => {
        setData(data.data)
        setLoading(false)
      })
    } catch (error) {
      console.error(error)
    }

    setLoading(false)
  }

  const create = async (content, file, rating) => {
    setLoading(true)

    const formData = new FormData()
    formData.append('comment', content)
    formData.append('file', file)
    formData.append('rating', rating)

    try {
      await http
        .post(`recipes/${id}/reviews`, formData, {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        })
        .then(() => {
          toast.success('Review added successfully')
          fetcher()
        })
    } catch (error) {
      toast.error('Failed to add review')
      console.error(error)
    }

    setLoading(false)
  }

  return {
    data,
    loading,
    fetcher,
    create
  }
}
