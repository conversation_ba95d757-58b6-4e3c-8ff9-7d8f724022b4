<?php

namespace App\Models;

use App\Enums\AffiliateType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AffiliateEarning extends Model
{
    protected $fillable = [
        'partner_id',
        'user_id',
        'affiliate_id',
        'order_id',
        'amount',
        'type',
        'commission_rate',
    ];

    protected $casts = [
        'type' => AffiliateType::class,
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function partner(): BelongsTo
    {
        return $this->belongsTo(Partner::class);
    }

    public function affiliate(): BelongsTo
    {
        return $this->belongsTo(Affiliate::class);
    }
}
