<?php

namespace App\Listeners;

use App\Enums\PartnerStatus;
use App\Events\StripeDisconnected;
use App\Models\Partner;
use App\Models\Subscription;
use App\Services\Partners\Stripe;
use App\Support\Facades\StripeHelper;

class CancelAndDeleteAllSubscriptionOnStripeDisconnectedListener
{
    public function handle(StripeDisconnected $event): void
    {
        if (! StripeHelper::canDisconnect()) {
            return;
        }

        $partners = Partner::query()
            ->with('stripeAccount')
            ->with('subscriptions')
            ->where('status', PartnerStatus::Activated)
            ->get();

        foreach ($partners as $partner) {
            $subscriptions = $partner->subscriptions;

            if (! $partner->stripeAccount) {
                Subscription::query()
                    ->where('partner_id', $partner->getKey())
                    ->delete();

                continue;
            }

            if (! $subscriptions) {
                continue;
            }

            $partnerStripe = new Stripe($partner);

            foreach ($subscriptions as $subscription) {
                if (! $subscription->auto_renew) {
                    $subscription->delete();

                    continue;
                }

                rescue(function () use ($subscription, $partnerStripe) {
                    $partnerStripe->updateSubscription($subscription->stripe_id, [
                        'cancel_at_period_end' => "true",
                    ]);
                });

                $subscription->delete();
            }
        }
    }
}
