import { forwardRef } from 'react'

const CirclePlusSolidIcon = forwardRef((props, ref) => {
  return (
    <svg ref={ref} {...props} width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="24" height="24" rx="12" fill="currentColor" />
      <path d="M18 11H13V6H11V11H6V13H11V18H13V13H18V11Z" fill="white" />
    </svg>
  )
})

CirclePlusSolidIcon.displayName = 'CirclePlusSolidIcon'

export { CirclePlusSolidIcon }
