<?php

namespace App\Http\Controllers\API\Recipes;

use App\Actions\Recipe\FilterCustomRecipe;
use App\Actions\Recipe\GetUserExcludeIngredients;
use App\Http\Controllers\APIController;
use App\Http\JsonResources\Recipes\RecipeJsonResource;
use App\Support\Facades\Core;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class CustomRecipeController extends APIController
{
    public function index(Request $request, FilterCustomRecipe $filterCustomRecipe): AnonymousResourceCollection
    {
        $user = $request->user();
        $user->loadMissing('metas');
        $userMetas = $user->metas;

        $parameters = $this->buildParameters($request, $user, $userMetas);

        $customRecipes = $filterCustomRecipe->handle([
            'partner_id' => Core::currentPartner()->getKey(),
            ...$parameters,
        ]);

        return RecipeJsonResource::collection($customRecipes);
    }

    private function buildParameters(Request $request, $user, $userMetas): array
    {
        $parameters = [];

        $parameters['excludeIngredients'] = $this->getExcludeIngredients($user, $userMetas);
        $parameters['diet'] = $this->getUserDiet($userMetas);

        if ($request->input('query')) {
            $parameters['query'] = $request->input('query');
        }

        return $parameters;
    }

    private function getExcludeIngredients($user, $userMetas): string
    {
        $userExcludeIngredients = (new GetUserExcludeIngredients())->handle($user);
        $userExcludeIngredients = array_merge(
            $userExcludeIngredients,
            $userMetas->where('name', 'recipes_excluded_ingredients')->pluck('value')->toArray()[0] ?? []
        );

        $userExcludeIngredients = array_unique($userExcludeIngredients);

        $availableExcludeIngredients = [];
        $tempIngredientsCheckStringLength = '';

        foreach ($userExcludeIngredients as $ingredient) {
            if (strlen($tempIngredientsCheckStringLength) + strlen($ingredient . ',') > 1000) {
                break;
            }

            $tempIngredientsCheckStringLength .= $ingredient . ',';
            $availableExcludeIngredients[] = $ingredient;
        }

        return implode(',', $availableExcludeIngredients);
    }

    private function getUserDiet($userMetas): string
    {
        $userDiet = $userMetas->where('name', 'recipes_diet')->pluck('value')->toArray()[0] ?? [];
        return implode('|', $userDiet);
    }
}
