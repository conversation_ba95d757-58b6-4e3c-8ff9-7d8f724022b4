import {
  Button,
  <PERSON><PERSON><PERSON>con,
  HeathDashboardWidget,
  Modal,
  RecipeSearchItem,
  SearchIcon,
  SpinnerBlock,
  Tab,
  TextInput,
  UserLayout,
  DashboardRecipeFilter,
  CloseIcon
} from '@/components'
import { useNavigate, useSearchParams } from 'react-router-dom'
import useHealthScore from '@/hooks/healthScore'
import useRecommendations from '@/hooks/recommendations'
import { RecommendationsFeatured } from '@/components/Recommendations'
import useRecipeMealTypes from '@/hooks/recipeMealTypes'
import { useEffect, useState } from 'react'
import useUserMetas from '@/hooks/userMetas'
import useAffiliate from '@/hooks/affiliate.js'
import { toast } from 'react-toastify'
import { useAuth } from '@/hooks/auth.js'
import useRecipes from '@/hooks/recipes.js'
import useRecipeParameters from '@/hooks/recipeParameters.js'
import useDebounce from '@/hooks/debounce.js'
import useRecipeDiet from '@/hooks/recipeDiet.js'
import useRecipeIngredients from '@/hooks/recipeIngredients.js'
import useReviews from '@/hooks/reviews.js'
import { requestForToken } from '../../firebase.js'
import useFirebase from '@/hooks/firebase.js'
import { requestNotificationPermission } from '@/utilities/notification.js'
import useSettings from '@/hooks/settings.js'

export default function DashboardPage() {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const { getUserMeta, loading: userMetaLoading } = useUserMetas()
  const { loading: healthLoading, data: healthData } = useHealthScore()
  const { loading: recommendationLoading, data: recommendationData } = useRecommendations()
  const { loading: recipeTypeLoading, data: types } = useRecipeMealTypes()
  const { processing, addReferralCode } = useAffiliate()
  const [tabs, setTabs] = useState([])
  const [healthQuestionnaireAnswered, setHealthQuestionnaireAnswered] = useState(false)
  const [referralData, setReferralData] = useState({
    referral_code: ''
  })

  const [currentTab, setCurrenTab] = useState('Main course')
  const [openFilter, setOpenFilter] = useState(false)
  const { user, verify } = useAuth({ middleware: 'auth' })
  const [modalReferralCode, setModalReferralCode] = useState(false)
  const { params: parameters, loading: parameterLoading, setParameters } = useRecipeParameters()
  const debouncedSearch = useDebounce(parameters, 500)

  const { data: diet, loading: dietLoading } = useRecipeDiet()
  const { data: ingredients, loading: ingredientsLoading, fetcher: ingredientFetcher } = useRecipeIngredients()

  const {
    recipes,
    loading: recipeLoading,
    hasPage,
    loadMorePage,
    viewMoreLoading,
    search
  } = useRecipes(
    {
      ...parameters
    },
    parameterLoading
  )

  const { data: reviews, loading: reviewsLoading } = useReviews()

  const { saveToken } = useFirebase()

  const settings = useSettings()

  const goToPage = (slug) => {
    return navigate('/pages/' + slug)
  }

  const toggleModal = (data) => {
    setOpenFilter(data)
  }

  const handleSubmitAddReferralCode = async (e) => {
    e.preventDefault()

    if (referralData.referral_code.length === 0) {
      toast.error('Referral code is required')
      return
    }

    const res = await addReferralCode(referralData.referral_code)

    if (res) {
      setModalReferralCode(false)
      await verify(true)
    }
  }

  const handleOnchangeTab = (type) => {
    setCurrenTab(type)

    search({
      ...parameters,
      mealTypes: [type]
    })
  }

  const handleDismissModal = async () => {
    setModalReferralCode(false)
  }

  const initReferralCode = async () => {
    if (user && !user.affiliate) {
      await addReferralCode()
      await verify(true)
    }
  }

  const applyFilter = async (parameters) => {
    setOpenFilter(false)
    setParameters({
      ...parameters,
      mealTypes: [currentTab]
    })
  }

  useEffect(() => {
    if (!recipeTypeLoading) {
      setTabs(
        Object.values(types).map((type) => {
          return {
            id: type,
            name: type
          }
        })
      )
    }
  }, [recipeTypeLoading])

  useEffect(() => {
    if (searchParams.get('social') === 'true') {
      localStorage.removeItem('login_via_code')
    }

    if (!userMetaLoading) {
      if (getUserMeta('first_login', 'true') === 'true') {
        return navigate('/account/first-update-personal')
      }

      if (getUserMeta('first_membership', 'true') === 'true') {
        return navigate('/account/get-started-membership')
      }
    }

    if (
      localStorage.getItem('health_questionnaire_answered') !== 'true' &&
      !userMetaLoading &&
      getUserMeta('questionnaire_answered', 'false') === 'true'
    ) {
      setHealthQuestionnaireAnswered(true)
      localStorage.setItem('health_questionnaire_answered', 'true')
    } else {
      localStorage.setItem('health_questionnaire_answered', 'false')
    }
  }, [userMetaLoading])

  useEffect(() => {
    if (user && !user.affiliate) {
      setModalReferralCode(true)
    }

    if (!userMetaLoading) {
      search({
        ...parameters,
        mealTypes: [currentTab]
      })
    }

    ingredientFetcher()
  }, [userMetaLoading])

  useEffect(() => {
    if (debouncedSearch) {
      search({
        ...parameters,
        mealTypes: [currentTab]
      })
    }
  }, [debouncedSearch])

  useEffect(() => {
    if (!modalReferralCode) {
      initReferralCode().then()
    }
  }, [modalReferralCode])

  useEffect(() => {
    requestNotificationPermission().then((response) => {
      if (localStorage.getItem('notification_enabled') === 'true' && response) {
        requestForToken().then((token) => (token ? saveToken(token) : null))
      }
    })
  }, [])

  if (userMetaLoading || parameterLoading) {
    return <SpinnerBlock />
  }

  return (
    <UserLayout>
      {settings.getSetting('member_benefits_page') && (
        <div
          onClick={() => goToPage(settings.getSetting('member_benefits_page'))}
          className="mx-5 mb-4 cursor-pointer rounded-2xl border-2 border-secondary px-3 py-2.5 text-center font-bold text-secondary"
        >
          Member Benefits
        </div>
      )}

      {!healthLoading ? (
        <HeathDashboardWidget
          questionnaireAnswered={healthQuestionnaireAnswered}
          healthData={healthData}
          className="px-5"
        />
      ) : (
        <div className="relative min-h-[100px]">
          <SpinnerBlock />
        </div>
      )}

      <div className="relative min-h-[80px]">
        {healthLoading ? (
          <SpinnerBlock />
        ) : (
          <div className="mb-4 bg-slate-200 p-5">
            {healthQuestionnaireAnswered ? (
              <div className="w-full">
                {recommendationLoading ? (
                  <div className="relative h-24 w-full">
                    <SpinnerBlock />
                  </div>
                ) : (
                  <div>
                    <div className="mb-1 text-2xl font-bold text-primary"> My Custom Recommendations </div>
                    <RecommendationsFeatured
                      recommendations={recommendationData?.allrecs}
                      onClickToRedirect={() => navigate('/health-and-wellness')}
                      title="Your Customer Recommendations"
                    />
                  </div>
                )}
              </div>
            ) : (
              <div>
                <div className="mb-4 text-sm">
                  The recommendations you see below are based on your
                  <Button
                    className="!font-normal !text-secondary"
                    variant="link"
                    onClick={() => navigate('/account/preferences')}
                  >
                    selected preferences
                  </Button>
                  . To get even more customized recommendations, complete our health questionnaire!
                </div>

                <Button
                  onClick={() => navigate('/health-questionnaire')}
                  className="w-full"
                  variant="primary"
                  size="lg"
                >
                  Complete the questionnaire!
                </Button>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="mb-28 px-5">
        <div className="mb-4">
          <div className="mb-4">
            <div className="mb-2 flex justify-between">
              <div className="text-xl font-bold">My Recommended Recipes</div>
              <Button
                disabled={recipeLoading}
                onClick={() => setOpenFilter(!openFilter)}
                variant="alternative"
                className="!border-none !bg-secondary !p-2 text-sm text-white no-underline"
              >
                <FilterIcon className="h-5 w-5" />
              </Button>
            </div>

            <div className="flex flex-wrap gap-2">
              {parameters.isLowOxalate && (
                <span
                  className="flex cursor-pointer items-center gap-2 rounded-2xl bg-[#E6EBF2] px-3 py-1 text-xs"
                  onClick={() => setParameters({ ...parameters, isLowOxalate: false })}
                >
                  Low Oxalate
                  <CloseIcon className="h-5 w-5" />
                </span>
              )}
              {parameters.isLowSulfur && (
                <span
                  className="flex cursor-pointer items-center gap-2 rounded-2xl bg-[#E6EBF2] px-3 py-1 text-xs"
                  onClick={() => setParameters({ ...parameters, isLowSulfur: false })}
                >
                  Low Sulfur
                  <CloseIcon className="h-4 w-4" />
                </span>
              )}
              {parameters.isLowHistamine && (
                <span
                  className="flex cursor-pointer items-center gap-2 rounded-2xl bg-[#E6EBF2] px-3 py-1 text-xs"
                  onClick={() => setParameters({ ...parameters, isLowHistamine: false })}
                >
                  Low Histamine
                  <CloseIcon className="h-4 w-4" />
                </span>
              )}
              {parameters.isLowSalicylate && (
                <span
                  className="flex cursor-pointer items-center gap-2 rounded-2xl bg-[#E6EBF2] px-3 py-1 text-xs"
                  onClick={() => setParameters({ ...parameters, isLowSalicylate: false })}
                >
                  Low Salicylate
                  <CloseIcon className="h-4 w-4" />
                </span>
              )}
              {parameters.isLowFodmap && (
                <span
                  className="flex cursor-pointer items-center gap-2 rounded-2xl bg-[#E6EBF2] px-3 py-1 text-xs"
                  onClick={() => setParameters({ ...parameters, isLowFodmap: false })}
                >
                  Low FODMAP
                  <CloseIcon className="h-4 w-4" />
                </span>
              )}
              {parameters.diet?.map((dietItem, index) => (
                <span
                  key={`diet-${index}`}
                  className="flex cursor-pointer items-center gap-2 rounded-2xl bg-[#E6EBF2] px-3 py-1 text-xs"
                  onClick={() =>
                    setParameters({
                      ...parameters,
                      diet: parameters.diet.filter((item) => item !== dietItem)
                    })
                  }
                >
                  {dietItem}
                  <CloseIcon className="h-4 w-4" />
                </span>
              ))}
              {parameters.allergens?.map((allergen, index) => (
                <span
                  key={`allergen-${index}`}
                  className="flex cursor-pointer items-center gap-2 rounded-2xl bg-[#E6EBF2] px-3 py-1 text-xs"
                  onClick={() =>
                    setParameters({
                      ...parameters,
                      allergens: parameters.allergens.filter((item) => item !== allergen)
                    })
                  }
                >
                  {allergen}
                  <CloseIcon className="h-4 w-4" />
                </span>
              ))}
              {parameters.ingredients?.map((ingredient, index) => (
                <span
                  key={`ingredient-${index}`}
                  className="flex cursor-pointer items-center gap-2 rounded-2xl bg-[#E6EBF2] px-3 py-1 text-xs"
                  onClick={() =>
                    setParameters({
                      ...parameters,
                      ingredients: parameters.ingredients.filter((item) => item !== ingredient)
                    })
                  }
                >
                  {ingredient}
                  <CloseIcon className="h-4 w-4" />
                </span>
              ))}
            </div>
          </div>

          <div className="mb-4 w-full">
            <TextInput
              classNameInput="!border-2 !border-[#E6EBF2] focus:!border-secondary"
              className="rounded-xl font-medium"
              suffix={<SearchIcon className="h-5 w-5" />}
              placeholder="Search"
              value={parameters.query || ''}
              onChange={(e) => setParameters({ ...parameters, query: e.target.value || ' ' })}
            />
          </div>
          {recipeTypeLoading ? (
            <div className="relative min-h-[80px] w-full">
              <SpinnerBlock />
            </div>
          ) : (
            <Tab currentTab={currentTab} onChange={(type) => handleOnchangeTab(type)} tabs={tabs}>
              <div className="relative mt-4 min-h-[80px] w-full">
                {recipeLoading || reviewsLoading || userMetaLoading || parameterLoading ? (
                  <SpinnerBlock />
                ) : recipes.length ? (
                  recipes.map((item, index) => {
                    return (
                      <RecipeSearchItem
                        hasAction={false}
                        hasRating={true}
                        reviews={reviews}
                        key={`recipe-item-${index}`}
                        recipe={item}
                        onRecipeClickToDetail={(id) => navigate(`/recipes/${id}`)}
                        className="mb-4"
                      />
                    )
                  })
                ) : (
                  <span className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">No Result</span>
                )}

                {!recipeLoading && hasPage() && (
                  <div className="mt-3 text-center">
                    <Button disabled={viewMoreLoading} onClick={loadMorePage}>
                      {viewMoreLoading ? 'Loading...' : 'View more'}
                    </Button>
                  </div>
                )}
              </div>
            </Tab>
          )}
        </div>
      </div>

      <Modal
        id="referral-code-modal !h-full"
        className="max-h-[80%] overflow-y-auto "
        backdrop={'static'}
        toggleModal={setModalReferralCode}
        shown={modalReferralCode}
      >
        <div className="relative px-4 pb-16">
          <div className="px-6 py-4 text-center text-3xl font-bold text-slate-900">Have a referral code?</div>
          <div className="mb-4 text-sm text-primary">
            If someone recommended our platform to you, please enter their referral code. This can only be done once and
            cannot be changed later.
          </div>

          <form onSubmit={handleSubmitAddReferralCode}>
            <div>
              <TextInput
                disabled={processing}
                type="text"
                name="referral_code"
                id="referral_code"
                placeholder="Referral Code"
                value={referralData.referral_code}
                onChange={(e) => setReferralData({ referral_code: e.target.value })}
              />
            </div>

            <Button disabled={processing} type="submit" size="lg" variant="secondary" className="mt-4 w-full">
              {processing ? 'Processing...' : 'Submit'}
            </Button>
          </form>

          <Button disabled={processing} size="lg" className="mt-4 w-full" variant="link" onClick={handleDismissModal}>
            Dismiss
          </Button>
        </div>
      </Modal>

      <Modal
        id="recipe-filter-modal"
        className="!h-full max-h-[80%] overflow-y-auto"
        toggleModal={toggleModal}
        backdrop={true}
        shown={openFilter}
      >
        <div className="relative">
          <div className="px-6 py-4 text-center text-3xl font-bold text-slate-900">Filter</div>
          <DashboardRecipeFilter
            data={parameters}
            diet={diet}
            dietLoading={dietLoading}
            ingredients={ingredients}
            ingredientsLoading={ingredientsLoading}
            applyFilter={applyFilter}
          />
        </div>
      </Modal>
    </UserLayout>
  )
}
