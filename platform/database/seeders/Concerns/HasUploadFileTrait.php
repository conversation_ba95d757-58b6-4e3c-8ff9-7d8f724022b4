<?php

namespace Database\Seeders\Concerns;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

trait HasUploadFileTrait
{
    public function uploadFile(string $filePath, string $path = 'recipes-plus', string $disk = 'public'): ?string
    {
        $filePath = database_path('seeders/files/' . $filePath);

        if (! File::exists($filePath)) {
            return null;
        }

        return Storage::disk($disk)->putFile($path, $filePath);
    }
}
