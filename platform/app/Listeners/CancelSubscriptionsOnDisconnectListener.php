<?php

namespace App\Listeners;

use App\Enums\SubscriptionStatus;
use App\Events\PartnerStripeDisconnected;
use App\Models\Subscription;
use App\Services\Partners\Stripe;
use App\Support\Facades\StripeHelper;
use Illuminate\Contracts\Queue\ShouldQueue;

class CancelSubscriptionsOnDisconnectListener implements ShouldQueue
{
    public function handle(PartnerStripeDisconnected $event): void
    {
        if (! StripeHelper::canDisconnect()) {
            return;
        }

        $partner = $event->partner;


        $subscriptions = Subscription::query()
            ->where('partner_id', $partner->getKey())
            ->get();

        $partnerStripe = new Stripe($partner);

        foreach ($subscriptions as $subscription) {
            if (! $subscription->auto_renew) {
                continue;
            }

            rescue(function () use ($subscription, $partnerStripe) {
                $partnerStripe->updateSubscription($subscription->stripe_id, [
                    'cancel_at_period_end' => "true",
                ]);
            });

            $subscription->update([
                'auto_renew' => false,
                'stripe_status' => SubscriptionStatus::CANCELED,
            ]);
        }
    }
}
