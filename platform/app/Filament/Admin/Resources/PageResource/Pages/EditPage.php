<?php

namespace App\Filament\Admin\Resources\PageResource\Pages;

use App\Actions\Page\UpdatePage;
use App\Filament\Admin\Resources\PageResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\ValidationException;

class EditPage extends EditRecord
{
    protected static string $resource = PageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        try {
            return (new UpdatePage())->update($record, $data);
        } catch (ValidationException) {
            Notification::make()
                ->title(__('Could not update page.'))
                ->danger()
                ->send();

            $this->halt();
        }
    }
}
