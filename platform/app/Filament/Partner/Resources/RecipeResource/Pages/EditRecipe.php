<?php

namespace App\Filament\Partner\Resources\RecipeResource\Pages;

use App\Actions\Recipe\GetImageDisk;
use App\Actions\Recipe\ParseDataUpsertRecipe;
use App\Actions\Recipe\UpdateImage;
use App\Actions\Recipe\UpsertCustomRecipe;
use App\Filament\Partner\Resources\RecipeResource;
use Filament\Facades\Filament;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class EditRecipe extends EditRecord
{
    protected static string $resource = RecipeResource::class;

    public function mutateFormDataBeforeFill(array $data): array
    {
        $data['ingredients'] = $this->parseIngredients($this->record->ingredients);
        $data['image_disk'] = $this->record->image_disk ?: GetImageDisk::get();

        $instructions = $this->record->instructions;

        $data['instructions'] = $this->parseInstructions($instructions);

        return $data;
    }

    protected function parseIngredients($ingredients): string
    {
        $nameList = [];

        foreach ($ingredients as $ingredient) {
            $amount = $ingredient->amount;

            if (Arr::get($amount, 'us.value') && Arr::get($amount, 'us.unit')) {
                $nameList[] = ($amount ? Arr::get($amount, 'us.value') . ' ' . Arr::get($amount, 'us.unit') : 1) . ' ' . $ingredient->name;
            } elseif (Arr::get($amount, 'us.amount') && Arr::get($amount, 'us.unitLong')) {
                $nameList[] = ($amount ? Arr::get($amount, 'us.amount') . ' ' . Arr::get($amount, 'us.unitLong') : 1) . ' ' . $ingredient->name;
            }
        }

        return implode(', ', $nameList);
    }

    protected function parseInstructions(array|string $instructions): string
    {
        if (is_array($instructions)) {
            $instructionsText = '';

            $steps = Arr::get($instructions, '0.steps') ?: [];

            foreach ($steps as $step) {
                $instructionsText .= $step['number'] . '. ' . $step['title'] . "\n";
            }
        } else {
            $instructionsText = $instructions;
        }

        return $instructionsText;
    }

    public function handleRecordUpdate(Model $model, array $data): Model
    {
        try {
            $imageDisk = Arr::get($data, 'image_disk');
            $payloadData = ParseDataUpsertRecipe::handle($data);

            $partner = Filament::getTenant();

            $recipe = (new UpsertCustomRecipe())->handle($partner, $payloadData, isUpdate: true);

            if ($image = Arr::get($data, 'image')) {
                (new UpdateImage())->handle($recipe, $image, $imageDisk);
            }

            $dataUpdate = [
                'tags' => Arr::get($data, 'tags'),
                'prevent_nutrition_flags_overwrite' => $preventNutritionFlagsOverwrite = Arr::get($data, 'prevent_nutrition_flags_overwrite'),
            ];

            if ($preventNutritionFlagsOverwrite) {
                $dataUpdate = [
                    ...$dataUpdate,
                    'is_low_oxalate' => Arr::get($data, 'is_low_oxalate'),
                    'is_low_histamine' => Arr::get($data, 'is_low_histamine'),
                    'is_low_sulfur' => Arr::get($data, 'is_low_sulfur'),
                    'is_low_salicylate' => Arr::get($data, 'is_low_salicylate'),
                    'is_low_fodmap' => Arr::get($data, 'is_low_fodmap'),
                ];
            }

            $recipe->update($dataUpdate);

            return $recipe;
        } catch (\Exception $e) {
            Notification::make()
                ->danger()
                ->title($e->getMessage())
                ->send();

            $this->halt();
        }
    }

    protected function getRedirectUrl(): ?string
    {
        return route('filament.partner.resources.recipes.edit', ['tenant' => Filament::getTenant(), 'record' => $this->record->id]);
        ;
    }
}
