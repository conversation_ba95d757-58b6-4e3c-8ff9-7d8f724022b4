<?php

namespace App\Filament\Partner\Resources\RecipeResource\Pages;

use App\Filament\Partner\Resources\RecipeResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Arr;

class ListRecipes extends ListRecords
{
    protected static string $resource = RecipeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function parseInstructions(array|string|null $instructions): string
    {
        if (is_array($instructions)) {
            $instructionsText = '';

            $steps = Arr::get($instructions, '0.steps') ?: [];

            foreach ($steps as $step) {
                $instructionsText .= $step['number'] . '. ' . $step['title'] . "\n";
            }
        } else {
            $instructionsText = $instructions;
        }

        return $instructionsText;
    }
}
