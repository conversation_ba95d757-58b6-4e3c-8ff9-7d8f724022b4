import {
  AppBar,
  AppLayout,
  Button,
  CopyInput,
  Label,
  LoadingPage,
  Modal,
  Navigation,
  TextInput
} from '@/components/index.js'
import { useAuth } from '@/hooks/auth.js'
import { useState } from 'react'
import { toast } from 'react-toastify'
import useAffiliate from '@/hooks/affiliate.js'
import useSettings from '@/hooks/settings.js'

export default function AffiliateProgramPage() {
  const { user, verify } = useAuth({ middleware: 'auth' })
  const { loading, getSetting } = useSettings()
  const { processing, addReferralCode } = useAffiliate()
  const [referralData, setReferralData] = useState({
    referral_code: ''
  })
  const [modalReferralCode, setModalReferralCode] = useState(false)

  const handleSubmitAddReferralCode = async (e) => {
    e.preventDefault()

    if (referralData.referral_code.length === 0) {
      toast.error('Referral code is required')
      return
    }

    const res = await addReferralCode(referralData.referral_code)

    if (res) {
      setModalReferralCode(false)
      await verify(true)
    }
  }

  if (loading) {
    return <LoadingPage />
  }

  return (
    <AppLayout>
      <AppBar leading={<AppBar.BackAction />}>
        <AppBar.Title>Affiliate Program</AppBar.Title>
      </AppBar>

      <div className="mb-28 mt-16 p-4">
        <div className="mb-3">
          <Label htmlFor="referral_code">Referral Code</Label>
          <CopyInput value={user.referral_code} />
        </div>

        {user.referral_code && getSetting('recipes_plus_app_url') && (
          <div className="mb-2">
            <Label htmlFor="referral_code">Referral URL</Label>
            <CopyInput value={getSetting('recipes_plus_app_url') + '?aff=' + user.referral_code} />
          </div>
        )}

        {!!(user && user.affiliate && !user.affiliate.referred_by) && (
          <div className="text-center">
            <Button className="!text-secondary" onClick={() => setModalReferralCode(true)} variant="link">
              Do you have any referral code?
            </Button>
          </div>
        )}
      </div>

      <Modal
        id="referral-code-modal !h-full"
        className="max-h-[80%] overflow-y-auto "
        backdrop={'static'}
        toggleModal={setModalReferralCode}
        shown={modalReferralCode}
      >
        <div className="relative px-4 pb-16">
          <div className="px-6 py-4 text-center text-3xl font-bold text-slate-900">Have a referral code?</div>
          <div className="mb-4 text-sm text-primary">
            If someone recommended our platform to you, please enter their referral code. This can only be done once and
            cannot be changed later.
          </div>

          <form onSubmit={handleSubmitAddReferralCode}>
            <div>
              <TextInput
                disabled={processing}
                type="text"
                name="referral_code"
                id="referral_code"
                placeholder="Referral Code"
                value={referralData.referral_code}
                onChange={(e) => setReferralData({ referral_code: e.target.value })}
              />
            </div>

            <Button disabled={processing} type="submit" size="lg" variant="secondary" className="mt-4 w-full">
              {processing ? 'Processing...' : 'Submit'}
            </Button>
          </form>

          <Button
            disabled={processing}
            size="lg"
            className="mt-4 w-full"
            variant="link"
            onClick={() => setModalReferralCode(false)}
          >
            Dismiss
          </Button>
        </div>
      </Modal>
      <Navigation />
    </AppLayout>
  )
}
