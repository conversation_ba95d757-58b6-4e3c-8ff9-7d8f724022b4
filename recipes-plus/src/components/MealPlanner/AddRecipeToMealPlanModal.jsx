import { forwardRef, useEffect, useState } from 'react'
import { Button, ChevronLeftIcon, ChevronRightIcon, MealPlanItem } from '@/components'
import PropTypes from 'prop-types'

const AddRecipeToMealPlanModal = forwardRef((props, ref) => {
  const { recipe, submit, close } = props
  const [days, setDays] = useState([])
  const [currenDay, setCurrentDay] = useState('')

  const formatDate = (date) => {
    const year = date.getFullYear()
    const month = ('0' + (date.getMonth() + 1)).slice(-2)
    const day = ('0' + date.getDate()).slice(-2)
    return `${year}-${month}-${day}`
  }

  const getCurrentDaysOfWeek = () => {
    const currentDate = new Date()

    setCurrentDay(formatDate(currentDate))

    const currentDayOfWeek = currentDate.getDay()

    const startOfWeek = new Date(currentDate)
    startOfWeek.setDate(currentDate.getDate() - (currentDayOfWeek === 0 ? 6 : currentDayOfWeek - 1))

    let weekDates = []
    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek)
      day.setDate(startOfWeek.getDate() + i)
      weekDates.push(day)
    }

    setDays(
      weekDates.map((date) => {
        return {
          name: date.toDateString().split(' ')[0].slice(0, 2),
          value: formatDate(date)
        }
      })
    )
  }

  const handleOnChange = (day) => {
    setCurrentDay(day)
  }

  const handleClose = () => {
    close()
  }

  const handleSubmit = (e) => {
    e.preventDefault()

    submit(currenDay)
  }

  useEffect(() => {
    getCurrentDaysOfWeek()
  }, [])

  return (
    <form onSubmit={handleSubmit} ref={ref} className="py-6">
      <div className="mb-12 text-center text-xl font-bold">Schedule recipe:</div>

      <div className="border-b px-4 pb-6">
        <MealPlanItem toggleRecipe={() => handleClose()} item={recipe} />

        <div className="mt-8 flex items-center justify-between">
          <div>
            <ChevronLeftIcon className="h-5 w-5" />
          </div>

          <div className="text-center">
            <div className="text-xl font-medium text-secondary">This week</div>
          </div>

          <div>
            <ChevronRightIcon className="h-5 w-5 text-primary" />
          </div>
        </div>
      </div>

      <div className="px-3">
        <div className="flex items-center justify-center gap-4 p-6">
          {days.map((day, index) => (
            <div
              onClick={() => handleOnChange(day.value)}
              key={`day-${index}`}
              className={` ${day.value === currenDay ? 'bg-secondary text-white' : ''} flex h-8 w-8 cursor-pointer items-center justify-center rounded-full border border-slate-200 text-xs font-normal uppercase`}
            >
              {day.name}
            </div>
          ))}
        </div>

        <Button className="mt-6 w-full" variant="secondary">
          Done
        </Button>
      </div>
    </form>
  )
})

AddRecipeToMealPlanModal.displayName = 'AddRecipeToMealPlanModal'
AddRecipeToMealPlanModal.propTypes = {
  recipe: PropTypes.object,
  submit: PropTypes.func,
  close: PropTypes.func
}

export { AddRecipeToMealPlanModal }
