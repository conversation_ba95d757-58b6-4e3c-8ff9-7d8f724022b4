<?php

namespace App\Enums;

enum PartnerStatus: string
{
    case Activated = 'activated';

    case Deactivated = 'deactivated';

    case Suspended = 'suspended';

    public function label(): string
    {
        return match ($this) {
            self::Activated => __('Activated'),
            self::Deactivated => __('Deactivated'),
            self::Suspended => __('Suspended'),
        };
    }
}
