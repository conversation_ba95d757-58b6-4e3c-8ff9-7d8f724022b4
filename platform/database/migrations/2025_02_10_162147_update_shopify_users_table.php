<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('shopify_users', function (Blueprint $table) {
            $table->string('shopify_customer_tag')->after('shopify_customer_id')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('shopify_users', function (Blueprint $table) {
            $table->dropColumn('shopify_customer_tag');
        });
    }
};
