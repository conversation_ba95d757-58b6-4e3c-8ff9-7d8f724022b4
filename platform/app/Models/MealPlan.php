<?php

namespace App\Models;

use App\Models\Concerns\BelongsToPartner;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MealPlan extends Model
{
    use BelongsToPartner;

    protected $table = 'meal_plans';

    protected $fillable = [
        'name',
        'user_id',
        'partner_id',
        'date',
        'cost',
    ];

    protected $casts = [
        'date' => 'date',
    ];

    public function nutrients(): BelongsToMany
    {
        return $this->belongsToMany(MealPlanNutrient::class, 'meal_plan_nutrient', 'meal_planner_id')->withPivot('amount', 'percent_of_daily_needs', 'unit');
    }

    public function recipes(): BelongsToMany
    {
        return $this->belongsToMany(Recipe::class, MealPlanRecipe::class, 'meal_planner_id')->withPivot(['ref_id', 'position', 'slot', 'servings']);
    }

    public function getCostTextAttribute(): string
    {
        if ($this->cost) {
            return '$' . number_format($this->cost, 2);
        }

        return '$0';
    }

    public function items(): HasMany
    {
        return $this->hasMany(MealPlanItem::class, 'meal_plan_id');
    }
}
