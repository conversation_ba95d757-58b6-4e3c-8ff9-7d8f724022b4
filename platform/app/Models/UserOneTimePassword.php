<?php

namespace App\Models;

use App\Contracts\Models\BelongsToPartner as BelongsToPartnerContract;
use App\Contracts\Models\BelongsToUser as BelongsToUserContract;
use App\Models\Concerns\BelongsToPartner;
use App\Models\Concerns\BelongsToUser;
use Illuminate\Database\Eloquent\Model;

class UserOneTimePassword extends Model implements
    BelongsToPartnerContract,
    BelongsToUserContract
{
    use BelongsToPartner;
    use BelongsToUser;

    /**
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'user_id' => 'int',
            'created_at' => 'datetime',
        ];
    }
}
