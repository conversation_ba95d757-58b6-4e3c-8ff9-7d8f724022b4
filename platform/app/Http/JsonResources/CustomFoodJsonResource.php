<?php

namespace App\Http\JsonResources;

use Illuminate\Http\Resources\Json\JsonResource;

class CustomFoodJsonResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->resource->id,
            'ref_id' => $this->resource->ref_id,
            'title' => $this->resource->title,
            'image' => $this->resource->image,
            'price' => $this->resource->price,
            'created_at' => $this->resource->created_at,
            'updated_at' => $this->resource->updated_at,
        ];
    }
}
