<?php

namespace App\Http\JsonResources\Recipes;

use Illuminate\Http\Resources\Json\JsonResource;

class RecipeCollectionJsonResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->resource->id,
            'name' => $this->resource->name,
            'image_url' => $this->resource->image_path ?: $this->resource->image_url,
            'recipes_count' => $this->resource->recipes_count,
            'recipes' => $this->whenLoaded('recipes', fn() => RecipeJsonResource::collection($this->recipes)),
        ];
    }
}
