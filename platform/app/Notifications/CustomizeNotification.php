<?php

namespace App\Notifications;

use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmMessage;

class CustomizeNotification extends Notification
{
    use Concerns\HasEnabledNotifications;

    public function __construct(public string $title, public string $body, ?string $action) {}

    public function toFcm(object $notifiable): FcmMessage
    {
        return FcmNotification::make()
            ->withTitle($this->title)
            ->withBody($this->body)
            ->withData([
                'action' => $this->action ?? null,
            ])
            ->build();
    }

    public function toArray(object $notifiable): array
    {
        return [
            'title' => $this->title,
            'body' => $this->body,
            'action' => $this->action ?? null,
        ];
    }
}
