<?php

namespace App\Listeners;

use App\Events\NotificationToolsSentEvent;
use App\Models\User;
use App\Models\UserFcmToken;
use App\Services\Partners\Fcm;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class HandleNotificationToolsSentListener
{
    public function handle(NotificationToolsSentEvent $event): void
    {
        $partner = $event->partner;
        $data = $event->data;

        $userIds = Arr::get($data, 'user_ids') ?: [];

        $isSendAllUser = false;

        if (! $userIds) {
            $isSendAllUser = true;
        }

        $userIds = is_array($userIds) ? array_filter($userIds) : Arr::wrap($userIds);

        $users = User::query()
            ->whereHas('partners', function ($query) use ($partner) {
                $query->where('id', $partner->getKey());
            });

        if ($userIds) {
            $users = $users->whereIn('id', $userIds);
        }

        $users = $users->get();

        if (! $users) {
            return;
        }

        $notificationTitle = Arr::get($data, 'title');
        $notificationBody = Arr::get($data, 'body');
        $notificationAction = Arr::get($data, 'action', []) ?: [];

        if (! $notificationTitle || ! $notificationBody) {
            return;
        }

        $fcmCloudMessage = new Fcm($partner);

        if ($isSendAllUser) {
            $report = $fcmCloudMessage->sendNotificationsToTopic('general', $notificationTitle, $notificationBody, $notificationAction);

            Log::info('Notification sent to all users', [
                'partner' => $partner->getKey(),
                'title' => $notificationTitle,
                'body' => $notificationBody,
                'action' => $notificationAction,
                'invalidTokens' => $report,
            ]);
        } else {
            $tokens = UserFcmToken::query()
                ->whereIn('user_id', $users->pluck('id')->all())
                ->pluck('token')
                ->all();

            $report = $fcmCloudMessage->sendNotification($tokens, $notificationTitle, $notificationBody, $notificationAction);

            Log::info('Notification sent to ' . count($users) . ' users', [
                'partner' => $partner->getKey(),
                'title' => $notificationTitle,
                'body' => $notificationBody,
                'action' => $notificationAction,
                'invalidTokens' => $report->invalidTokens(),
                'validTokens' => $report->validTokens(),
            ]);
        }
    }
}
