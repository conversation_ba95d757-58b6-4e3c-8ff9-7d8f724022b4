<?php

namespace App\Http\JsonResources\Recipes;

use App\Http\JsonResources\PartnerJsonResource;
use App\Models\PartnerMeta;
use App\ValueObjects\RecipeInstruction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;

class RecipeJsonResource extends JsonResource
{
    public function toArray($request): array
    {
        $isModel = $this->resource instanceof Model;

        $model = $this->resource;

        $author = null;

        if ($isModel) {
            if ($model->user_id && $model->partner_id) {
                $author = $this->whenLoaded('partner') ? PartnerJsonResource::make($this->resource->partner) : null;
            }
        }

        $data = [
            'id' => $this->resource->id,
            'model_id' => $this->resource->model_id ?? null,
            'spoonacular_id' => $this->resource->spoonacular_id ?? null,
            'ref_id' => $this->resource->ref_id ?? null,
            'partner_id' => $partnerId = ($this->resource->partner_id ?? null),
            'title' => $this->resource->title,
            'author' => $author,
            'image' => $this->resource->image_url ?? $this->resource->image,
            'summary' => $this->resource->summary,
            'servings' => $this->resource->servings,
            'position' => $isModel ? $this->resource?->pivot?->position : null,
            'slot' => $isModel ? $this->resource?->pivot?->slot : null,
            'ready_in_minutes' => $this->resource->{$isModel ? 'ready_in_minutes' : 'readyInMinutes'},
            'health_score' => $this->resource->{$isModel ? 'health_score' : 'healthScore'},
            'spoonacular_score' => $this->resource->{$isModel ? 'spoonacular_score' : 'spoonacularScore'},
            'price_per_serving' => $this->resource->{$isModel ? 'price_per_serving' : 'pricePerServing'},
            'weight_watcher_smart_points' => $this->resource->{$isModel ? 'weight_watcher_smart_points' : 'weightWatcherSmartPoints'},
            'instructions' => $this->when($this->resource->instructions && is_array($this->instructions), fn() => RecipeInstructionJsonResource::make(RecipeInstruction::createFromArray(Arr::first($this->instructions)))),
            'type' => 'Recipe',
        ];

        if ($isModel) {
            $user = request()->user();

            $appUrl = PartnerMeta::query()
                ->where('partner_id', $partnerId)
                ->where('name', 'recipes_plus_app_url')
                ->value('value') ?? null;

            $recipeSharingUrl = null;

            if ($appUrl && $user && ($refCode = $user->referral_code)) {
                $recipeSharingUrl = $appUrl . sprintf('/recipes/%s/share', $this->resource->ref_id);

                $recipeSharingUrl .= '?ref=' . $refCode;
            }

            $data = [...$data,
                'reviews_count' =>  $this->resource->reviews_count,
                'reviews_avg' => (float) $this->resource->reviews_avg_rating ?: 0,
                'recipe_sharing_url' => $recipeSharingUrl,
            ];
        }

        return $data;
    }
}
