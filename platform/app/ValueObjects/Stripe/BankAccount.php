<?php

namespace App\ValueObjects\Stripe;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Arr;

class BankAccount implements Arrayable
{
    public function __construct(
        readonly public string $id,
        readonly public string $accountHolderName,
        readonly public ?string $accountHolderType,
        readonly public ?string $accountType,
        readonly public string $bankName,
        readonly public ?string $country,
        readonly public ?string $currency,
        readonly public ?string $customerId,
        readonly public ?string $fingerprint,
        readonly public ?string $routingNumber,
        readonly public string $status,
        readonly public string|array|null $metadata,
    ) {}

    public static function createFromArray(array $data): static
    {
        return new static(
            Arr::get($data, 'id'),
            Arr::get($data, 'account_holder_name'),
            Arr::get($data, 'account_holder_type'),
            Arr::get($data, 'account_type'),
            Arr::get($data, 'bank_name'),
            Arr::get($data, 'country'),
            Arr::get($data, 'currency'),
            Arr::get($data, 'customer'),
            Arr::get($data, 'fingerprint'),
            Arr::get($data, 'routing_number'),
            Arr::get($data, 'status'),
            Arr::get($data, 'metadata'),
        );
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'account_holder_name' => $this->accountHolderName,
            'account_holder_type' => $this->accountHolderType,
            'account_type' => $this->accountType,
            'bank_name' => $this->bankName,
            'country' => $this->country,
            'currency' => $this->currency,
            'customer' => $this->customerId,
            'fingerprint' => $this->fingerprint,
            'routing_number' => $this->routingNumber,
            'status' => $this->status,
            'metadata' => $this->metadata,
        ];
    }
}
