import { forwardRef } from 'react'
import PropTypes from 'prop-types'

const InvalidFeedbackText = forwardRef((props, ref) => {
  const { className, errors, ...attributes } = props

  let cx = className || ''

  cx += ' my-1 list-none space-y-1 text-sm text-danger-500'

  if (!errors) {
    return null
  }

  return (
    <ul ref={ref} className={cx} {...attributes}>
      {errors.map((error, index) => {
        return <li key={`invalid-feedback-text-${index}`}>{error}</li>
      })}
    </ul>
  )
})

InvalidFeedbackText.displayName = 'InvalidFeedbackText'

InvalidFeedbackText.propTypes = {
  className: PropTypes.string,
  errors: PropTypes.array
}

export { InvalidFeedbackText }
