<?php

namespace App\Services\Recipe;

use App\Actions\Recipe\FilterCustomRecipe;
use App\Actions\Recipe\GetExcludeIngredientsByChemical;
use App\Actions\Recipe\MappingRecipes;
use App\Enums\UserMetaKey;
use App\Helpers\AllergenIngredientHelper;
use App\Helpers\RecipeIngredientHelper;
use App\Jobs\SyncRecipeFromSpoonacular;
use App\Models\Partner;
use App\Models\Recipe as RecipeModel;
use App\Models\User;
use App\Services\Spoonacular;
use App\ValueObjects\Recipe;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class RecipeFilterService
{
    public function __construct(
        protected Spoonacular $spoonacular,
        protected FilterCustomRecipe $filterCustomRecipe,
        protected GetExcludeIngredientsByChemical $getExcludeIngredientsByChemical,
    ) {}

    public function filter(Request $request, Partner $partner): LengthAwarePaginator
    {
        $userExcludeIngredients = $this->getExcludeIngredients($request);

        $parameters = $this->buildBaseParameters($request);
        $parameters = $this->addExcludeIngredientsToParameters($parameters, $userExcludeIngredients);
        $parameters = $this->addQueryParameters($request, $parameters);
        $parameters = $this->addDietParameters($request, $parameters);
        $parameters = $this->addCalorieParameters($request, $parameters);
        $parameters = $this->addPaginationParameters($request, $parameters);

        $customRecipes = $this->filterCustomRecipe->handle([
            'partner_id' => $partner->getKey(),
            ...$parameters,
        ]);

        $sponacularParameters = [
            ...Arr::except($parameters, [
                'page',
                'is_low_oxalate',
                'is_low_histamine',
                'is_low_sulfur',
                'is_low_salicylate',
                'is_low_fodmap',
            ]),
            'fillIngredients' => 'true',
            'addRecipeInformation' => 'true',
        ];

        $spoonacularRecipes = $this->spoonacular
            ->withPartner($partner)
            ->searchRecipes($sponacularParameters);

        $this->syncRecipes($partner, $spoonacularRecipes->items());

        return MappingRecipes::handle($customRecipes, $spoonacularRecipes);
    }

    protected function buildBaseParameters(Request $request): array
    {
        return [
            'page' => $request->input('page', 1),
            'is_low_oxalate' => $request->boolean('is_low_oxalate'),
            'is_low_histamine' => $request->boolean('is_low_histamine'),
            'is_low_sulfur' => $request->boolean('is_low_sulfur'),
            'is_low_salicylate' => $request->boolean('is_low_salicylate'),
            'is_low_fodmap' => $request->boolean('is_low_fodmap'),
        ];
    }

    protected function getExcludeIngredients(Request $request): array
    {
        $userExcludeIngredients = [];

        $allergens = $request->input('allergens', []);

        foreach ($allergens as $allergen) {
            $userExcludeIngredients = [...$userExcludeIngredients, ...AllergenIngredientHelper::getSubIngredients(Str::of($allergen)->lower()->snake()->toString())];
        }

        if ($request->boolean('is_low_oxalate')) {
            $userExcludeIngredients = [
                ...$userExcludeIngredients,
                ...$this->getExcludeIngredientsByChemical->handle('oxalate', 1),
            ];
        }

        if ($request->boolean('is_low_histamine')) {
            $userExcludeIngredients = [
                ...$userExcludeIngredients,
                ...$this->getExcludeIngredientsByChemical->handle('histamine', 1),
            ];
        }

        if ($request->boolean('is_low_sulfur')) {
            $userExcludeIngredients = [
                ...$userExcludeIngredients,
                ...$this->getExcludeIngredientsByChemical->handle('sulfur', 1),
            ];
        }

        if ($request->boolean('is_low_salicylate')) {
            $userExcludeIngredients = [
                ...$userExcludeIngredients,
                ...$this->getExcludeIngredientsByChemical->handle('salicylate', 1),
            ];
        }

        if ($request->boolean('is_low_fodmap')) {
            $userExcludeIngredients = [
                ...$userExcludeIngredients,
                ...$this->getExcludeIngredientsByChemical->handle('fodmap', 1),
            ];
        }

        if ($request->boolean('withUserPreferences')) {
            $userExcludeIngredients = $this->getUserExcludeIngredients($request->user(), $userExcludeIngredients);
        }

        return array_unique($userExcludeIngredients);
    }

    protected function getUserExcludeIngredients(User $user, array $existingIngredients): array
    {
        $user->loadMissing('metas');
        $userMetas = $user->metas;

        $data = [
            ...$existingIngredients,
            ...$userMetas->where('name', 'recipes_excluded_ingredients')->pluck('value')->toArray()[0] ?? [],
        ];

        if ($userMetas->where('name', 'is_low_oxalate')) {
            $data = [
                ...$data,
                ...$this->getExcludeIngredientsByChemical->handle('oxalate', 1),
            ];
        }

        if ($userMetas->where('name', 'is_low_histamine')) {
            $data = [
                ...$data,
                ...$this->getExcludeIngredientsByChemical->handle('histamine', 1),
            ];
        }

        if ($userMetas->where('name', 'is_low_sulfur')) {
            $data = [
                ...$data,
                ...$this->getExcludeIngredientsByChemical->handle('sulfur', 1),
            ];
        }

        if ($userMetas->where('name', 'is_low_salicylate')) {
            $data = [
                ...$data,
                ...$this->getExcludeIngredientsByChemical->handle('salicylate', 1),
            ];
        }

        if ($userMetas->where('name', 'is_low_fodmap')) {
            $data = [
                ...$data,
                ...$this->getExcludeIngredientsByChemical->handle('fodmap', 1),
            ];
        }

        return $data;
    }

    protected function addExcludeIngredientsToParameters(array $parameters, array $excludeIngredients): array
    {
        $excludeIngredients = (new RecipeIngredientHelper())->optimizeIngredientList($excludeIngredients);

        $parameters['excludeIngredients'] = implode(',', $excludeIngredients);

        return $parameters;
    }

    protected function addQueryParameters(Request $request, array $parameters): array
    {
        if ($query = $request->input('query')) {
            $parameters['query'] = $query;
        }

        if ($request->has('addRecipeInformation')) {
            $parameters['addRecipeInformation'] = $request->input('addRecipeInformation');
        }

        if ($request->has('sort')) {
            $parameters['sort'] = $request->input('sort');
        }

        if ($includeIngredients = $request->input('includeIngredients')) {
            $parameters['includeIngredients'] = implode(',', $includeIngredients);
        }

        if ($type = $request->input('mealTypes')) {
            $parameters['type'] = is_array($type) ? implode('|', $type) : $type;
        }

        return $parameters;
    }

    protected function addDietParameters(Request $request, array $parameters): array
    {
        $diet = $request->input('diet', []);

        if ($request->boolean('is_low_fodmap')) {
            $diet[] = 'Low FODMAP';
        }

        if ($request->boolean('withUserPreferences')) {
            $user = $request->user();
            $user->loadMissing('metas');
            $userMetas = $user->metas;

            if ($userMetas->where('name', UserMetaKey::RECIPE_LOW_FODMAP_ENABLED)->first()?->value) {
                $diet[] = 'Low Oxalate';
            }

            $currentDiet = Arr::get($parameters, 'diet', []);
            $userDiet = $userMetas->where('name', 'recipes_diet')->pluck('value')->toArray()[0] ?? [];
            $diet = array_merge($diet, $userDiet, $currentDiet);
        }

        $parameters['diet'] = implode(',', array_unique($diet));

        return $parameters;
    }

    protected function addCalorieParameters(Request $request, array $parameters): array
    {
        if ($minCalories = $request->input('minCalories')) {
            $parameters['minCalories'] = $minCalories;
        }

        if ($maxCalories = $request->input('maxCalories')) {
            $parameters['maxCalories'] = $maxCalories;
        }

        return $parameters;
    }

    protected function addPaginationParameters(Request $request, array $parameters): array
    {
        $parameters['offset'] = $request->integer('offset');

        $parameters['number'] = $request->integer('number', 10);

        return $parameters;
    }

    protected function filterSpoonacularRecipes(array $recipes, array $parameters): array
    {
        $data = [];

        $recipeIngredientHelper = new RecipeIngredientHelper();

        foreach ($recipes as $recipe) {
            $ingredients = $this->getIngredientsByRecipe($recipe);

            if ($parameters['is_low_oxalate'] && ! $recipeIngredientHelper->isLowOxalate($ingredients)) {
                continue;
            }

            if ($parameters['is_low_histamine'] && ! $recipeIngredientHelper->isLowHistamine($ingredients)) {
                continue;
            }

            if ($parameters['is_low_sulfur'] && ! $recipeIngredientHelper->isLowSulfur($ingredients)) {
                continue;
            }

            if ($parameters['is_low_salicylate'] && ! $recipeIngredientHelper->isLowSalicylate($ingredients)) {
                continue;
            }

            if ($parameters['is_low_fodmap'] && ! $recipeIngredientHelper->isLowFodmap($ingredients)) {
                continue;
            }

            $data[] = Recipe::createFromArray($recipe);
        }

        return $data;
    }

    protected function getIngredientsByRecipe(array $recipe): array
    {
        if (!$ingredients = Arr::get($recipe, 'extendedIngredients')) {
            return [];
        }

        return array_column($ingredients, 'name');
    }

    protected function parseArrayToPagination(array $data, int $page = 1, int $perPage = 10, int $total = 0): LengthAwarePaginator
    {
        $currentPage = $page;
        $total = $total ?: count($data);
        $offset = ($currentPage * $perPage) - $perPage;

        return new LengthAwarePaginator(
            array_slice($data, $offset, $perPage, true),
            $total,
            $perPage,
            $currentPage,
        );
    }

    public function syncRecipes(Partner $partner, array $recipes): array
    {
        $data = $this->getRecipesNotSynchronized($recipes);

        if (empty($data)) {
            return [];
        }

        foreach ($data as $item) {
            $data = $item instanceof Recipe ? $item->toArray() : Recipe::createFromArray($item)->toArray();

            SyncRecipeFromSpoonacular::dispatch($partner, $data);
        }

        return $data;
    }

    public function getRecipesNotSynchronized(array $recipes): array
    {
        $existingRecipeIds = RecipeModel::query()
            ->whereIn('ref_id', array_column($recipes, 'id'))
            ->pluck('ref_id')
            ->toArray();

        return array_filter($recipes, fn($recipe) => !in_array($recipe->id, $existingRecipeIds));
    }
}
