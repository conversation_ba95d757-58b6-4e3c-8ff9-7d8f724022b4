import { useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { useEffect, useState } from 'react'
import {
  Quantity,
  Image,
  AppLayout,
  LoadingPage,
  SpinnerBlock,
  Tab,
  HeartIcon,
  Modal,
  AddRecipeToCollectionModal,
  RecipeCollectionCreateModal,
  RecipeReviewInput,
  AddRecipeToMealPlanModal,
  Button,
  PlusIcon,
  ShareIcon,
  ChevronLeftIcon,
  SEO
} from '@/components'
import { useRecipeDetail } from '@/hooks/recipeDetail'
import useRecipeIngredients from '@/hooks/recipeIngredients'
import useRecipeNutrients from '@/hooks/recipeNutrients'
import useRecipeCollections from '@/hooks/recipeCollections'
import useRecipeReviews from '@/hooks/recipeReviews'
import StarRatings from 'react-star-ratings/build/star-ratings.js'
import useRecipeMealPlan from '@/hooks/recipeMealPlanner.js'

export default function GuestRecipeDetailPage() {
  const { id } = useParams()
  const navigate = useNavigate()
  const [partialTabLoading, setPartialTabLoading] = useState(true)
  const [isHeaderSticky, setIsHeaderSticky] = useState(false)
  const [tabActive, setTabActive] = useState('recipe-details')
  const [isSharing, setIsSharing] = useState(false)
  const [queryParams] = useSearchParams()
  const nutrientOverview = ['Calories', 'Sugar', 'Protein']
  const nutrientsNoUnit = ['Calories']

  const { recipe, servingOrigin, servingCount, setServingCount, loading } = useRecipeDetail(id, true)

  const { data: ingredients, fetcher: fetchIngredients } = useRecipeIngredients(id, true)
  const { data: nutrients, fetcher: fetchNutrients } = useRecipeNutrients(id, true)
  const { data: reviews, create: submitReview, fetcher: fetchReviews } = useRecipeReviews(id)
  const { create: createCollection } = useRecipeCollections({ fetchCollection: false })
  const { addRecipe: addRecipeToMealPlaner } = useRecipeMealPlan()
  const [collectionForm, setCollectionForm] = useState({ name: '' })

  const [openModalRecipeAction, setOpenModalRecipeAction] = useState(false)
  const [openModalAddRecipeToCollection, setOpenModalAddRecipeToCollection] = useState(false)
  const [openModalAddRecipeToMealPlaner, setOpenModalAddRecipeToMealPlaner] = useState(false)
  const [openModalCreateCollection, setOpenModalCreateCollection] = useState(false)

  const [displayPopup, setDisplayPopup] = useState(false)

  if (queryParams.has('aff')) {
    localStorage.setItem('referral_code', queryParams.get('aff'))
  }

  const tabs = [
    {
      id: 'recipe-details',
      name: 'Recipe Details'
    },
    {
      id: 'nutritional',
      name: 'Nutritional Info'
    },
    {
      id: 'reviews',
      name: 'Reviews'
    }
  ]

  const getPartialTabData = async () => {
    switch (tabActive) {
      case 'reviews':
        if (reviews !== undefined) {
          return
        }

        setPartialTabLoading(true)

        await fetchReviews()

        setPartialTabLoading(false)
        break

      default:
        if (!ingredients.length) {
          setPartialTabLoading(true)

          await fetchIngredients()

          setPartialTabLoading(false)
        }

        if (!nutrients.length) {
          setPartialTabLoading(true)

          await fetchNutrients()

          setPartialTabLoading(false)
        }

        break
    }
  }

  const isTabActive = (id) => {
    if (partialTabLoading) {
      return false
    }

    return tabActive === id
  }

  const handleCreateCollection = () => {
    createCollection({ name: collectionForm.name })
    setCollectionForm({ name: '' })
    setOpenModalCreateCollection(false)
    setOpenModalAddRecipeToCollection(true)
  }

  const handleCreateMealPlaner = (date) => {
    addRecipeToMealPlaner({
      date: date,
      recipeId: recipe.id
    })
    setOpenModalAddRecipeToMealPlaner(false)
  }

  const handleShare = async () => {
    if (isSharing) {
      return
    }
    setIsSharing(true)
    if (navigator.share) {
      try {
        await navigator.share({
          title: recipe.title,
          text: recipe.summary,
          url: window.location.href
        })
        console.log('Content shared successfully!')
      } catch (error) {
        console.error('Error sharing:', error)
      } finally {
        setIsSharing(false)
      }
    } else {
      alert('Web Share API is not supported in this browser.')
      setIsSharing(false)
    }
  }

  useEffect(() => {
    if (!loading) {
      getPartialTabData().then()
    }
  }, [tabActive, loading])

  useEffect(() => {
    if (openModalAddRecipeToCollection) {
      setOpenModalRecipeAction(false)
      setOpenModalCreateCollection(false)
    }
  }, [openModalAddRecipeToCollection])

  useEffect(() => {
    if (openModalRecipeAction) {
      setOpenModalAddRecipeToCollection(false)
      setOpenModalCreateCollection(false)
    }
  }, [openModalRecipeAction])

  useEffect(() => {
    if (openModalAddRecipeToMealPlaner) {
      setOpenModalRecipeAction(false)
    }
  }, [openModalAddRecipeToMealPlaner])

  useEffect(() => {
    if (openModalCreateCollection) {
      setOpenModalAddRecipeToCollection(false)
      setOpenModalRecipeAction(false)
    }
  }, [openModalCreateCollection])

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY

      setIsHeaderSticky(scrollPosition > 0)
    }

    window.addEventListener('scroll', handleScroll)

    handleScroll()

    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  useEffect(() => {
    setTimeout(() => {
      setDisplayPopup(true)
      document.body.style.overflow = 'hidden'
    }, 5000)

    return () => {
      document.body.style.overflow = 'auto'
    }
  }, [])

  useEffect(() => {
    if (localStorage.getItem('access_token') && localStorage.getItem('refresh_token')) {
      navigate('/recipes/' + id)
    }
  }, [])

  if (loading) {
    return <LoadingPage />
  }

  return (
    <AppLayout>
      <SEO title={recipe.title} description={recipe.summary} image={recipe.image} url={window.location.href} />
      {!isHeaderSticky && (
        <div className="absolute flex h-20 w-full flex-col justify-center px-2 py-4">
          <div className="flex justify-between gap-2">
            <Button
              onClick={() => navigate(-1)}
              className=" relative !h-10 !w-10 !rounded-full !bg-[#FFFFFFCC] !text-primary"
            >
              <ChevronLeftIcon className="absolute left-1/2 top-1/2 h-5 w-5 -translate-x-1/2 -translate-y-1/2" />
            </Button>

            {!isTabActive('recipe-details') && (
              <div className="line-clamp-2 overflow-hidden text-ellipsis break-words px-2 text-lg font-bold leading-[20px] text-primary">
                {recipe.title}
              </div>
            )}

            <div className="flex gap-1">
              <Button
                onClick={() => navigate('/auth/login')}
                className="relative !h-10 !w-10 !rounded-full"
                variant="secondary"
              >
                <HeartIcon className="absolute left-1/2 top-1/2 h-5 w-5 -translate-x-1/2 -translate-y-1/2" />
              </Button>

              <Button
                onClick={() => navigate('/auth/login')}
                className="relative !h-10 !w-10 !rounded-full"
                variant="secondary"
              >
                <PlusIcon className="absolute left-1/2 top-1/2 h-6 w-6 -translate-x-1/2 -translate-y-1/2" />
              </Button>

              <Button onClick={() => handleShare()} className="relative !h-10 !w-10 !rounded-full" variant="secondary">
                <ShareIcon className="absolute left-1/2 top-1/2 h-6 w-6 -translate-x-1/2 -translate-y-1/2" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {isHeaderSticky && (
        <div
          className={`3xl:max-w-4xl fixed z-10 flex h-20 w-full max-w-lg flex-col justify-center px-2 py-4 shadow-xl xl:max-w-2xl ${isTabActive('recipe-details') ? `bg-[#FFFFFFCC]` : `bg-white`}`}
        >
          <div className="flex items-center justify-between gap-2">
            <Button
              onClick={() => navigate(-1)}
              className=" relative !h-10 !w-10 !rounded-full !bg-[#ffffff80] !text-primary"
            >
              <ChevronLeftIcon className="absolute left-1/2 top-1/2 h-5 w-5 -translate-x-1/2 -translate-y-1/2" />
            </Button>

            <div className="line-clamp-2 overflow-hidden text-ellipsis break-words px-2 text-lg font-bold leading-[20px] text-primary">
              {recipe.title}
            </div>

            <div className="flex gap-1">
              <Button
                onClick={() => navigate('/auth/login')}
                className="relative !h-10 !w-10 !rounded-full"
                variant="secondary"
              >
                <HeartIcon className="absolute left-1/2 top-1/2 h-5 w-5 -translate-x-1/2 -translate-y-1/2" />
              </Button>

              <Button
                onClick={() => navigate('/auth/login')}
                className="relative !h-10 !w-10 !rounded-full"
                variant="secondary"
              >
                <PlusIcon className="absolute left-1/2 top-1/2 h-6 w-6 -translate-x-1/2 -translate-y-1/2" />
              </Button>

              <Button onClick={() => handleShare()} className="relative !h-10 !w-10 !rounded-full" variant="secondary">
                <ShareIcon className="absolute left-1/2 top-1/2 h-6 w-6 -translate-x-1/2 -translate-y-1/2" />
              </Button>
            </div>
          </div>
        </div>
      )}

      <div className={`mb-28 ${!isTabActive('recipe-details') ? `mt-16` : ``}`}>
        {displayPopup && (
          <div className="3xl:max-w-4xl fixed top-1/2 z-10 max-w-lg -translate-y-1/2 bg-secondary p-6 duration-300 xl:max-w-2xl">
            <div className="mb-2 text-center text-lg font-semibold text-white">Enjoy this recipe?</div>

            <div className="mb-2 text-center text-sm font-light text-white">
              Sign up now to take our health questionnaire and get thousands of recipes like this one, customized to
              match your health and diet preferences!
            </div>

            <div className="flex justify-center">
              <Button
                onClick={() => navigate('/auth/register')}
                variant="alternative"
                size="md"
                className="border-2 bg-transparent  !text-opacity-100 hover:!text-primary"
              >
                Sign up now
              </Button>
            </div>
          </div>
        )}

        {isTabActive('recipe-details') && (
          <div>
            <div
              className="h-72 w-full bg-slate-200 bg-cover bg-no-repeat"
              style={{ backgroundImage: `url(${recipe.image})` }}
            ></div>
          </div>
        )}
        <div className="p-4">
          {isTabActive('recipe-details') && (
            <>
              <div className="mb-3 text-4xl font-bold leading-[30px]"> {recipe.title}</div>

              {recipe.author && recipe.author?.name && (
                <div className="my-6 flex items-center gap-2">
                  <div className="w-10 shrink-0">
                    <Image src={recipe.author.profile_photo_url} rounded="full" alt={recipe.author.name} />
                  </div>
                  <div>
                    <div className="font-bold">{recipe.author.name}</div>
                  </div>
                </div>
              )}
            </>
          )}

          <Tab tabs={tabs} currentTab="recipe-details" onChange={(id) => setTabActive(id)}>
            {partialTabLoading && (
              <div className="relative min-h-[80px] w-full">
                <SpinnerBlock />
              </div>
            )}

            {isTabActive('recipe-details') && (
              <>
                <div className="mt-6">
                  <div className="mb-4 flex items-center justify-between">
                    <div className="text-2xl font-semibold italic">Ingredients</div>
                    <div className="flex items-center gap-2">
                      <Quantity quantity={servingCount} setQuantity={setServingCount} />

                      <span className="text-sm">{servingCount === 1 ? `serving` : 'servings'}</span>
                    </div>
                  </div>

                  {ingredients.map((ingredient, key) => {
                    const amount = ((ingredient.amount.us.value / servingOrigin) * servingCount).toFixed(2)

                    let ingredientValue = `${amount} (${ingredient.amount.metric.value} ${ingredient.amount.metric.unit}) ${ingredient.amount.us.unit}`

                    if (ingredient.amount.metric.unit === ingredient.amount.us.unit) {
                      ingredientValue = `${amount} ${ingredient.amount.us.unit}`
                    }

                    return (
                      <div className="mb-4" key={key}>
                        {ingredientValue} {ingredient.name}
                      </div>
                    )
                  })}
                </div>

                <div className="mt-6">
                  <div className="mb-3 text-2xl font-semibold italic">Directions</div>
                  {recipe.instructions &&
                    recipe.instructions.steps &&
                    recipe.instructions.steps.map((step, key) => {
                      return (
                        <div className="mb-6" key={`direction-step-${key}`}>
                          <div className="mb-4 text-lg font-semibold"> Step {step.number}</div>
                          {/* <div className="mb-4">
                              <Image ratio="16:9" src="/images/recipe.jpg" alt="Recipe" />
                            </div>
                            */}
                          <div className="text-primary text-opacity-80">{step.title}</div>
                        </div>
                      )
                    })}

                  {!recipe.instructions && (
                    <div className="text-primary text-opacity-60">No instructions available</div>
                  )}
                </div>

                <div className="mb-6 mt-6 font-bold"> Nutrition Overview</div>

                <div className="flex flex-nowrap items-center gap-4 overflow-x-auto pb-2">
                  {nutrients
                    .filter((nutrient) => nutrientOverview.includes(nutrient.name))
                    .map((nutrient, key) => {
                      return (
                        <div className="relative h-32 w-32 flex-shrink-0 bg-slate-200" key={key}>
                          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-center leading-[1.2]">
                            <span className="text-4xl font-bold">
                              {((nutrient.amount / servingOrigin) * servingCount).toFixed(0)}
                              {!nutrientsNoUnit.includes(nutrient.name) ? nutrient.unit : ''}{' '}
                            </span>
                            <div className="text-center text-sm capitalize">
                              {' '}
                              <span className="text-base font-bold">{nutrient.name}</span>{' '}
                              <span className="whitespace-nowrap text-xs lowercase">per serving</span>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                </div>
              </>
            )}

            {isTabActive('nutritional') && (
              <div className="mt-6">
                <div className="mb-3 flex justify-between border-b pb-3">
                  <div className="text-xs uppercase">Per Serving</div>
                  <div className="flex items-center gap-2">
                    <div className="text-end text-xs uppercase">Amount</div>
                    <div className="w-12 text-end text-xs uppercase">Rda</div>
                  </div>
                </div>

                {nutrients.map((nutrient, key) => {
                  return (
                    <div key={key}>
                      <div className="mb-2 flex items-center justify-between">
                        <div> {nutrient.name}</div>
                        <div className="flex items-center gap-2">
                          <div className="text-end">
                            {(nutrient.amount / servingOrigin).toFixed(2)} {nutrient.unit}
                          </div>
                          <div className="w-12 text-end">
                            {nutrient.percent_of_daily_needs.toFixed(0) + '%' || 'N/A'}
                          </div>
                        </div>
                      </div>

                      {/* <div className="mb-2 flex items-center justify-between">
                      <div className="ms-2 text-sm text-primary text-opacity-80"> Saturated Fat</div>
                      <div className="flex items-center gap-2">
                        <div className="text-end text-sm text-primary text-opacity-80"> 100 cal</div>
                        <div className="w-12 text-end text-sm text-primary text-opacity-80"> 20%</div>
                      </div>
                    </div>

                    <div className="mb-2 flex items-center justify-between">
                      <div className="ms-2 text-sm text-primary text-opacity-80"> Trans Fat</div>
                      <div className="flex items-center gap-2">
                        <div className="text-end text-sm text-primary text-opacity-80"> 70 cal</div>
                        <div className="w-12 text-end text-sm text-primary text-opacity-80"> 19%</div>
                      </div>
                    </div> */}
                    </div>
                  )
                })}

                <div className="mb-4 mt-4 font-bold"> Nutrition Overview</div>

                <div className="flex flex-nowrap items-center gap-4 overflow-x-auto pb-2">
                  {nutrients
                    .filter((nutrient) => nutrientOverview.includes(nutrient.name))
                    .map((nutrient, key) => {
                      return (
                        <div className="relative h-32 w-32 flex-shrink-0 bg-slate-200" key={key}>
                          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
                            <span className="text-4xl font-bold">
                              {((nutrient.amount / servingOrigin) * servingCount).toFixed(2)}
                              {!nutrientsNoUnit.includes(nutrient.name) ? nutrient.unit : ''}{' '}
                            </span>
                            <div className="text-center text-sm capitalize"> {nutrient.name}</div>
                          </div>
                        </div>
                      )
                    })}
                </div>
              </div>
            )}

            {isTabActive('reviews') && (
              <div className="mt-6">
                <div className="mb-4 text-lg font-bold"> Reviews</div>

                {reviews !== undefined && reviews.length > 0 ? (
                  reviews.map((review, key) => {
                    return (
                      <div className="mb-4 border-b pb-4" key={key}>
                        <div className="mb-2 flex items-start justify-between">
                          <div className="flex items-center gap-4">
                            <div>
                              <div className="w-10 shrink-0">
                                <Image src={review.user.profile_photo_url} rounded="full" alt="Author review" />
                              </div>
                            </div>

                            <div>
                              <div>
                                <div className="font-bold">{review.user.name}</div>
                                <StarRatings
                                  rating={review.rating}
                                  isSelectable={true}
                                  starDimension="16px"
                                  starSpacing="2px"
                                  starRatedColor="#f7ac32"
                                  starHoverColor="#f7ac32"
                                  numberOfStars={5}
                                  name="rating"
                                />
                              </div>
                            </div>
                          </div>
                          <div className="text-xs text-primary text-opacity-60">{review.created_at_text}</div>
                        </div>
                        {review.attachments.length > 0 && (
                          <div className="mb-2 w-full">
                            <Image ratio="16:9" src={review.attachments[0]['image_url']} alt="" />
                          </div>
                        )}
                        <div className="mt-2 text-primary text-opacity-80"> {review.comment}</div>
                      </div>
                    )
                  })
                ) : (
                  <div className="text-center text-primary text-opacity-60"> No reviews yet</div>
                )}

                <RecipeReviewInput submit={(content, file, rating) => submitReview(content, file, rating)} />
              </div>
            )}
          </Tab>

          {/*<div className="mb-6">*/}
          {/*  <div className="mb-4 font-bold"> Ingredient Details</div>*/}

          {/*  <div className="mb-3 flex items-end justify-between border-b border-slate-800 pb-3">*/}
          {/*    <div className="font-bold"> Dried Basil</div>*/}
          {/*    <div className="text-xs font-bold"> 1 teaspoon</div>*/}
          {/*  </div>*/}

          {/*  <div className="flex items-center justify-between border-b py-3">*/}
          {/*    <div> Oxalate Level</div>*/}
          {/*    <div className="bg-yellow-400 bg-opacity-50 px-2 py-1 uppercase"> LOW</div>*/}
          {/*  </div>*/}

          {/*  <div className="flex items-center justify-between border-b py-3">*/}
          {/*    <div> Sulfur Level</div>*/}
          {/*    <div className="bg-red-400 bg-opacity-50 px-2 py-1 uppercase"> HIGH</div>*/}
          {/*  </div>*/}

          {/*  <div className="flex items-center justify-between border-b py-3">*/}
          {/*    <div> Histamine</div>*/}
          {/*    <div className="bg-green-400 bg-opacity-50 px-2 py-1 uppercase"> MEDIUM</div>*/}
          {/*  </div>*/}

          {/*  <div className="flex items-start justify-between border-b py-3">*/}
          {/*    <div> Histamine</div>*/}
          {/*    <div>*/}
          {/*      <div> Paleo</div>*/}
          {/*      <div> Vegetarian</div>*/}
          {/*    </div>*/}
          {/*  </div>*/}
          {/*</div>*/}
        </div>
      </div>

      <Modal
        id="add-recipe-to-meal-planner"
        shown={openModalAddRecipeToMealPlaner}
        toggleModal={() => setOpenModalAddRecipeToMealPlaner(!openModalAddRecipeToMealPlaner)}
      >
        <div>
          <AddRecipeToMealPlanModal
            submit={(date) => handleCreateMealPlaner(date)}
            recipe={recipe}
            close={() => setOpenModalAddRecipeToMealPlaner(false)}
          />
        </div>
      </Modal>

      <Modal
        id="add-recipe-to-collection-modal"
        shown={openModalAddRecipeToCollection}
        toggleModal={() => setOpenModalAddRecipeToCollection(!openModalAddRecipeToCollection)}
      >
        <AddRecipeToCollectionModal
          recipeId={recipe.id}
          triggerCreateCollection={() => setOpenModalCreateCollection(true)}
          onSubmitted={() => setOpenModalAddRecipeToCollection(false)}
        />
      </Modal>

      <Modal
        id="add-collection-modal"
        shown={openModalCreateCollection}
        toggleModal={() => setOpenModalCreateCollection(!openModalCreateCollection)}
      >
        <RecipeCollectionCreateModal
          inputOnChange={(name) => setCollectionForm({ ...collectionForm, name: name })}
          submit={handleCreateCollection}
        />
      </Modal>
    </AppLayout>
  )
}
