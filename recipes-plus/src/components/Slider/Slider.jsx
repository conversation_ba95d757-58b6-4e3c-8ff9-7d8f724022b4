import { forwardRef, useEffect, useState } from 'react'
import PropTypes from 'prop-types'
import { Button, Toggle } from '@/components'

const Slider = forwardRef((props, ref) => {
  const { className, name, label, value, onChange, openCustomizeModal, enabledCustomList, ...attributes } = props

  const [state, setState] = useState(value || '1')
  const [isEnable, setIsEnable] = useState(!!value)

  let enabledCustomListAction = enabledCustomList ?? true

  let cx = className || ''

  cx += ' rounded-2xl bg-slate-200 p-6'

  let sliderClasses = 'h-6 bg-secondary rounded-2xl absolute'

  let dotClasses = 'w-8 h-8 bg-secondary rounded-2xl border border-white absolute top-1/2 -translate-y-1/2'

  if (!isEnable) {
    dotClasses += ' !bg-slate-300'
  }

  switch (!isEnable ? '1' : value) {
    case '1':
      dotClasses += ' left-0'
      sliderClasses += ' w-0'
      break
    case '2':
      dotClasses += ' left-1/2 -translate-x-1/2'
      sliderClasses += ' w-1/2'
      break

    case '3':
      dotClasses += ' right-0'
      sliderClasses += ' w-full'
      break
  }

  const handleTriggerCustomizeModal = () => {
    openCustomizeModal(name)
  }

  const handleOnChange = (level = null) => {
    if (!isEnable) {
      onChange('')

      return
    }

    onChange(level ?? state)
  }

  const handleToggle = (e, level) => {
    e.preventDefault()

    if (!isEnable) {
      return
    }

    if (state === level && state !== value) {
      handleOnChange(level)
    }

    setState(level)
  }

  const handleOnOff = (e) => {
    e.preventDefault()

    setIsEnable(e.target.checked)
    setState(state || '1')
  }

  useEffect(() => {
    handleOnChange()
  }, [state, isEnable])

  useEffect(() => {
    setIsEnable(!!value)
    setState(value)
  }, [value])

  return (
    <div className={cx} ref={ref} {...attributes}>
      <div className="mb-6 flex items-center justify-between">
        {label && <div className="font-bold">{label}</div>}
        <Toggle checked={!!isEnable} onChange={(e) => handleOnOff(e)} className="bg-white" />
      </div>

      <div className="relative z-10 h-6 w-full rounded-2xl bg-white">
        <button
          onClick={(e) => handleToggle(e, '1')}
          className="absolute left-0 top-1/2 z-20 h-8 w-1/3 -translate-y-1/2 bg-white opacity-0"
        ></button>

        <button
          onClick={(e) => handleToggle(e, '2')}
          className="absolute left-1/3 top-1/2 z-20 h-8 w-1/3 -translate-y-1/2 bg-white opacity-0"
        ></button>

        <button
          onClick={(e) => handleToggle(e, '3')}
          className="absolute left-2/3 top-1/2 z-20 h-8 w-1/3 -translate-y-1/2 bg-white opacity-0"
        ></button>
        <div className={sliderClasses}></div>
        <span className={dotClasses}></span>
      </div>

      <div className="top-8 mt-4 w-full">
        <div className="flex justify-between">
          <div
            className={`relative text-sm uppercase ${value === '1' ? `font-semibold text-primary` : 'font-light text-slate-400'}`}
          >
            <span className="absolute -top-6 left-1/2 z-0 h-6 w-0.5 -translate-x-1/2 bg-slate-300"></span>
            Low
          </div>
          <div
            className={`relative text-sm uppercase ${value === '2' ? `font-semibold text-primary` : 'font-light text-slate-400'}`}
          >
            <span className="absolute -top-6 left-1/2 z-0 h-6 w-0.5 -translate-x-1/2 bg-slate-300"></span>
            Medium
          </div>
          <div
            className={`relative text-sm uppercase ${value === '3' ? `font-semibold text-primary` : 'font-light text-slate-400'}`}
          >
            <span className="absolute -top-6 left-1/2 z-0 h-6 w-0.5 -translate-x-1/2 bg-slate-300"></span>
            High
          </div>
        </div>
      </div>

      {enabledCustomListAction && (
        <div className="mt-3 text-center">
          <Button
            disabled={!isEnable}
            onClick={() => handleTriggerCustomizeModal()}
            variant="link"
            className={`text-sm !font-light text-secondary no-underline ${!isEnable ? 'opacity-50' : ''}`}
          >
            Let me customize selection
          </Button>
        </div>
      )}
    </div>
  )
})

Slider.displayName = 'Slider'

Slider.propTypes = {
  className: PropTypes.string,
  label: PropTypes.string,
  name: PropTypes.string,
  value: PropTypes.string,
  onChange: PropTypes.func,
  openCustomizeModal: PropTypes.func,
  enabledCustomList: PropTypes.bool
}

export { Slider }
