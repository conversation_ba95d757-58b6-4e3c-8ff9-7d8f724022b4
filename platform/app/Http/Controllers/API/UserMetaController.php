<?php

namespace App\Http\Controllers\API;

use App\Actions\Recipe\GetIncludeIngredientsByChemical;
use App\Http\Controllers\APIController;
use App\Http\JsonResources\UserMetaJsonResource;
use App\Http\Requests\API\UserMetaRequest;
use App\Http\Responses\Errors\ForbiddenResponse;
use App\Models\UserMeta;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;
use Symfony\Component\HttpFoundation\Response;

class UserMetaController extends APIController
{
    public function __construct(protected GetIncludeIngredientsByChemical $includeIngredientsByChemical) {}

    public function show(Request $request, int|string $user): AnonymousResourceCollection|Response
    {
        if ($user !== 'me') {
            return ForbiddenResponse::make();
        }

        $user = $request->user();
        $user->loadMissing('metas');

        return UserMetaJsonResource::collection($user->metas);
    }

    public function store(UserMetaRequest $request, int|string $user): AnonymousResourceCollection|Response
    {
        if ($user !== 'me') {
            return ForbiddenResponse::make();
        }

        $user = $request->user();

        $data = $request->validated();

        $data = $this->handleIngredientsData($data);

        $values = [];

        foreach ($data as $key => $value) {
            $values[] = [
                'name' => $key,
                'user_id' => $user->getKey(),
                'value' => (new UserMeta(['value' => $value]))->getAttributes()['value'],
            ];
        }

        UserMeta::query()->upsert($values, ['name', 'user_id'], ['value']);

        $user->loadMissing('metas');

        return UserMetaJsonResource::collection($user->metas);
    }

    protected function handleIngredientsData(array $data): array
    {
        $recipeKeys = [
            'oxalate',
            'histamine',
            'sulfur',
            'salicylate',
            'fodmap',
        ];

        foreach ($recipeKeys as $key) {
            $isEnabled = Arr::get($data, "recipe_low_{$key}_enabled");

            if ($isEnabled && ! Arr::get($data, "recipe_low_{$key}_customizable")) {
                $data["recipe_low_{$key}_customizable"]
                    = count(Arr::get($data, "recipe_low_{$key}_customize_ingredients", [])) !== count($this->includeIngredientsByChemical->handle($key, 1));
            }
        }

        return $data;
    }
}
