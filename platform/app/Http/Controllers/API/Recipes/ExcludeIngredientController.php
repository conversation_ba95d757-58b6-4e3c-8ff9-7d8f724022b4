<?php

namespace App\Http\Controllers\API\Recipes;

use App\Actions\Recipe\GetExcludeIngredientsByChemical;
use App\Http\Controllers\Controller;
use App\Http\Responses\JsonResponse;
use App\Models\Ingredient;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class ExcludeIngredientController extends Controller
{
    public function __invoke(Request $request): JsonResponse
    {
        $request->validate([
            'name' => ['required', 'string', Rule::in(['sulfur', 'histamine', 'oxalate'])],
            'value' => ['nullable', 'numeric'],
        ]);

        if (($name = $request->input('name')) && ($value = $request->input('value'))) {
            $ingredients = (new GetExcludeIngredientsByChemical())->handle(
                $name,
                $value,
            );
        } else {
            $ingredients = Ingredient::query();
            $ingredients = match ($name) {
                'sulfur' => $ingredients->orderBy('sulfur')->pluck('sulfur', 'name'),
                'histamine' => $ingredients->orderBy('histamine')->pluck('histamine', 'name'),
                'oxalate' => $ingredients->orderBy('oxalate')->pluck('oxalate', 'name'),
                default => $ingredients,
            };

            $ingredients = $ingredients->toArray();
        }

        return new JsonResponse([
            'data' => $ingredients,
        ]);
    }
}
