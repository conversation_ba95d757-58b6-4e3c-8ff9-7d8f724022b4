import { forwardRef } from 'react'
import { ArrowLeftIcon, Button } from '@/components'
import { useNavigate } from 'react-router-dom'

const AppBarBackAction = forwardRef((props, ref) => {
  const navigate = useNavigate()
  return (
    <Button ref={ref} className="!p-3" onClick={() => navigate(-1)} variant="link">
      <ArrowLeftIcon className="h-5 w-5" />
    </Button>
  )
})

AppBarBackAction.displayName = 'AppBarBackAction'

export { AppBarBackAction }
