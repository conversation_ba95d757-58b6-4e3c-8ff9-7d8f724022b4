<?php

namespace App\Listeners;

use App\Events\UpsertRecipeToSpoonacularErrorEvent;
use App\Models\User;
use Filament\Notifications\Events\DatabaseNotificationsSent;
use Filament\Notifications\Notification;

class UpsertRecipeToSpoonacularErrorListener
{
    public function handle(UpsertRecipeToSpoonacularErrorEvent $event)
    {
        if ($event->object instanceof User) {
            $user = $event->object;
        } else {
            $user = $event->object->users->where('email', $event->object->email)->first();
        }

        Notification::make()
            ->icon('heroicon-o-x-circle')
            ->title(__(':count custom recipe(s) could not be synchronized with Spoonacular', [
                'count' => count($event->data),
            ]))
            ->body(__('Please check the error logs for more information.'))
            ->danger()
            ->sendToDatabase($user);

        event(new DatabaseNotificationsSent($user));
    }
}
