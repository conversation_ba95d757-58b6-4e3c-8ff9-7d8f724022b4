import { forwardRef } from 'react'

const MessageIcon = forwardRef((props, ref) => {
  return (
    <svg ref={ref} {...props} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M14.9188 7.37598L11.2161 10.3868C10.5165 10.9418 9.5322 10.9418 8.83262 10.3868L5.09863 7.37598"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.0907 17.5C16.6251 17.507 18.3333 15.4246 18.3333 12.8653V7.14168C18.3333 4.58235 16.6251 2.5 14.0907 2.5H5.90924C3.37478 2.5 1.66663 4.58235 1.66663 7.14168V12.8653C1.66663 15.4246 3.37478 17.507 5.90924 17.5H14.0907Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
})

MessageIcon.displayName = 'MessageIcon'

export { MessageIcon }
