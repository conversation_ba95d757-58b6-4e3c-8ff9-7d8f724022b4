import { useEffect, useState } from 'react'
import useAuthStore from '@/stores/auth.js'
import http from '@/utilities/http.js'

export default function useReviews() {
  const [data, setData] = useState({})
  const [loading, setLoading] = useState(true)
  const { accessToken } = useAuthStore()

  const fetcher = async () => {
    setLoading(true)

    try {
      await http
        .get('recipes/reviews', {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        })
        .then(({ data }) => {
          setData(data.data)
          setLoading(false)
        })
    } catch (error) {
      console.error(error)
    }

    setLoading(false)
  }

  useEffect(() => {
    fetcher().then()
  }, [])

  return {
    data,
    loading
  }
}
