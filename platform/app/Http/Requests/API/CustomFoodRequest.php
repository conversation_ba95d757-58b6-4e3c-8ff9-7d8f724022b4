<?php

namespace App\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;

class CustomFoodRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'title' => ['required', 'string'],
            'image' => ['nullable', 'string'],
            'nutrition' => ['nullable', 'array'],
            'nutrition.*.name' => ['required', 'string'],
            'nutrition.*.value' => ['required', 'numeric'],
            'diets' => ['nullable', 'array'],
            'diets.*' => ['required', 'string'],
            'intolerances' => ['nullable', 'array'],
            'intolerances.*' => ['required', 'string'],
            'price' => ['required', 'numeric', 'min:0'],
        ];
    }
}
