<?php

namespace App\Filament\Partner\Resources;

use App\Enums\AffiliateType;
use App\Filament\Partner\Resources\AffiliateEarningResource\Pages;
use App\Filament\Partner\Widgets\PartnerAffiliateReportOverviewWidget;
use App\Models\AffiliateEarning;
use Carbon\Carbon;
use Filament\Facades\Filament;
use Filament\Forms\Components\DatePicker;
use Filament\Resources\Resource;
use Filament\Support\Enums\IconPosition;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class AffiliateEarningResource extends Resource
{
    protected static ?string $model = AffiliateEarning::class;

    protected static ?string $tenantOwnershipRelationshipName = 'partner';

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar-square';

    protected static ?string $slug = 'affiliate-earnings/reports';

    public static function getNavigationGroup(): ?string
    {
        return __('Affiliates');
    }

    public static function getLabel(): ?string
    {
        return __('Affiliate Earning Reports');
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('ID'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label(__('Referred by'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('affiliate.user.name')
                    ->label(__('User'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('order_id')
                    ->prefix('#')
                    ->iconPosition(IconPosition::After)
                    ->openUrlInNewTab()
                    ->icon('heroicon-o-arrow-top-right-on-square')
                    ->url(fn(AffiliateEarning $record): string => route(
                        'filament.partner.resources.subscriptions.index',
                        [
                            'tenant' => Filament::getTenant(),
                            'tableSearch' => $record->order_id,
                        ],
                    ))
                    ->label(__('Subscription ID'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('commission_rate')
                    ->suffix('%')
                    ->label(__('Rate')),
                Tables\Columns\TextColumn::make('amount')
                    ->money('USD')
                    ->label(__('Amount'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('type')
                    ->label(__('Type'))
                    ->badge()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\Filter::make('created_at')
                    ->form([
                        DatePicker::make('created_from')
                            ->label(__('Start date'))
                            ->placeholder(fn($state): string => 'Dec 18, ' . now()->subYear()->format('Y')),
                        DatePicker::make('created_until')
                            ->label(__('End date'))
                            ->placeholder(fn($state): string => now()->format('M d, Y')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'] ?? null,
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'] ?? null,
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['created_from'] ?? null) {
                            $indicators['created_from'] = __('Affiliate reports since :date', ['date' => Carbon::parse($data['created_from'])->toFormattedDateString()]);
                        }
                        if ($data['created_until'] ?? null) {
                            $indicators['created_until'] = __('Affiliate reports until :date', ['date' => Carbon::parse($data['created_until'])->toFormattedDateString()]);
                        }

                        return $indicators;
                    }),
                Tables\Filters\SelectFilter::make('type')
                    ->options(function () {
                        $options = [];

                        foreach (AffiliateType::cases() as $enum) {
                            $options[$enum->value] = $enum->getLabel();
                        }

                        return $options;
                    })
                    ->label(__('Type')),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getWidgets(array $filter = []): array
    {
        return [
            PartnerAffiliateReportOverviewWidget::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAffiliates::route('/'),
        ];
    }
}
