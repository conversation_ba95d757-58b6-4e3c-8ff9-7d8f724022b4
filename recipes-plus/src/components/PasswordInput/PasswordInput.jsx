import { forwardRef, useState } from 'react'
import { EyeCloseIcon, EyeIcon, LockIcon } from '@/components/Icon'
import { TextInput } from '@/components'

const PasswordInput = forwardRef((props, ref) => {
  const [type, setType] = useState('password')

  const toggle = () => {
    setType(type === 'password' ? 'text' : 'password')
  }

  return (
    <TextInput
      ref={ref}
      type={type}
      prefix={<LockIcon className="h-5 w-5" />}
      suffix={
        <button type="button" onClick={toggle}>
          {type === 'text' ? <EyeCloseIcon className="h-5 w-5" /> : <EyeIcon className="h-5 w-5" />}
        </button>
      }
      {...props}
    />
  )
})

PasswordInput.displayName = 'PasswordInput'

export { PasswordInput }
